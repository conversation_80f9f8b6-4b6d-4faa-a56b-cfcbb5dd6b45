/*--------------------------------------------------------------
>>> TABLE OF CONTENTS:
----------------------------------------------------------------
# Utility
# Cards
# Common
# Form
# Navigations
# Animations
# Mobile Nav
# Search Popup
# Page Header
# Google Map
# Client Carousel
--------------------------------------------------------------*/

/*--------------------------------------------------------------
# Common
--------------------------------------------------------------*/
:root {
    --sopot-font: "Montserrat", sans-serif;
    --sopot-font-two: "Montserrat", sans-serif;
    --sopot-gray: #787878;
    --sopot-gray-rgb: 120, 120, 120;
    --sopot-white: #ffffff;
    --sopot-white-rgb: 255, 255, 255;
    --sopot-black: #001d23;
    --sopot-black-rgb: 0, 29, 35;
    --sopot-pink: #FA28A9;
    --sopot-base: #208936;
    --sopot-base-rgb: 247, 221, 10;
}

.row {
    --bs-gutter-x: 30px;
}

.gutter-y-30 {
    --bs-gutter-y: 30px;
}

body {
    font-family: var(--sopot-font);
    color: var(--sopot-gray);
    font-size: 17px;
    line-height: 26px;
    font-weight: 400;
}

body.locked {
    overflow: hidden;
}

a {
    color: var(--sopot-gray);
}

a,
a:hover,
a:focus,
a:visited {
    text-decoration: none;
}

::placeholder {
    color: inherit;
    opacity: 1;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    color: var(--sopot-black);
    margin: 0;
    font-family: var(--sopot-font-two);
    font-weight: 700;
}

p {
    margin: 0;
}

dl,
ol,
ul {
    margin-top: 0;
    margin-bottom: 0;
}
.error{
    color: #FA28A9;
}
.pd-100-0-100 {
    padding: 100px 0 100px;
}

.pd-0-0-100 {
    padding: 0 0 100px;
}

::placeholder {
    color: inherit;
    opacity: 1;
}

.page-wrapper {
    position: relative;
    margin: 0 auto;
    width: 100%;
    min-width: 300px;
    overflow: hidden;
}

.container {
    padding-left: 15px;
    padding-right: 15px;
}

@media (min-width: 1200px) {
    .container {
        max-width: 1200px;
    }
}

::placeholder {
    color: inherit;
    opacity: 1;
}

.section-separator {
    border-color: var(--sopot-border);
    border-width: 1px;
    margin-top: 0;
    margin-bottom: 0;
}

#particles-js {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background-position: 50% 50%;
    opacity: 0.4;
    z-index: -1;
}

.thm-btn {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    -webkit-appearance: none;
    outline: none !important;
    background-color: var(--sopot-base);
    color: var(--sopot-white);
    font-size: 16px;
    font-weight: 400;
    padding: 17px 50px 17px;
    transition: all .35s;
    overflow: hidden;
    border-radius: 10px;
    text-transform: capitalize;
    z-index: 1;
}

.thm-btn:hover {
    color: var(--sopot-white);
}

.thm-btn:after,
.thm-btn:before {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    top: 0;
    left: -100%;
    background-color: var(--sopot-pink);
    z-index: -1;
    transition: all .35s;
}

.thm-btn:before {
    opacity: .5;
}

.thm-btn:hover:after {
    transition-delay: .3s;
}

.thm-btn:hover:after,
.thm-btn:hover:before {
    left: 0;
}

.section-title {
    margin-top: -6px;
    margin-bottom: 50px;
}

.section-title__tagline {
    font-size: 20px;
    color: var(--sopot-base);
    font-weight: 500;
    text-transform: uppercase;
    font-style: normal;
}

.section-title__title {
    position: relative;
    display: block;
    margin: 0;
    font-size: 35px;
    line-height: 45px;
    margin-top: 3px;
}

.bootstrap-select .btn-light:not(:disabled):not(.disabled).active,
.bootstrap-select .btn-light:not(:disabled):not(.disabled):active,
.bootstrap-select .show>.btn-light.dropdown-toggle {
    box-shadow: none !important;
    outline: none !important;
}

.bootstrap-select>.dropdown-toggle {
    box-shadow: none !important;
}

.bootstrap-select .dropdown-toggle:focus,
.bootstrap-select>select.mobile-device:focus+.dropdown-toggle {
    outline: none !important;
}

.bootstrap-select .dropdown-menu {
    border: 0;
    padding-top: 0;
    padding-bottom: 0;
    margin-top: 0;
    z-index: 991;
    border-radius: 0;
}

.bootstrap-select .dropdown-menu>li+li>a {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.bootstrap-select .dropdown-menu>li.selected>a {
    background-color: rgba(var(--sopot-base-rgb), .20);
    color: var(--sopot-base);
}

.bootstrap-select .dropdown-menu>li>a {
    font-size: 16px;
    font-weight: 500;
    padding: 4px 20px;
    color: #ffffff;
    background: var(--sopot-black);
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
}

.bootstrap-select .dropdown-menu>li>a:hover {
    background: var(--sopot-base);
    color: #fff;
    cursor: pointer;
}

.preloader {
    position: fixed;
    background-color: #fff;
    background-position: center center;
    background-repeat: no-repeat;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9991;
    display: -webkit-box;
    display: flex;
    -webkit-box-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    align-items: center;
    text-align: center;
}

.preloader__image {
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: flipInY;
    animation-name: flipInY;
    -webkit-animation-duration: 2s;
    animation-duration: 2s;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
}

/* scroll to top */
.scroll-to-top {
    display: inline-block;
    width: 45px;
    height: 45px;
    background: var(--sopot-base);
    position: fixed;
    bottom: 40px;
    right: 40px;
    z-index: 99;
    text-align: center;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
    display: none;
    border-radius: 50%;
    transition: all 0.4s ease;
}

.scroll-to-top i {
    color: #ffffff;
    font-size: 18px;
    line-height: 45px;
}

.scroll-to-top:hover {
    background-color: var(--sopot-black);
}

.scroll-to-top:hover i {
    color: #fff;
}

/*--------------------------------------------------------------
# Navigations One
--------------------------------------------------------------*/
.main-header {
    position: relative;
    display: block;
    background: var(--sopot-white);
    width: 100%;
    z-index: 999;
    transition: all 500ms ease;
}

.main-header__top {
    position: relative;
    display: block;
    background-color: #0c0f16;
    z-index: 1;
}

.main-header__top-inner {
    position: relative;
    display: block;
    padding: 7.5px 0;
}

.main-header__top-left {
    position: relative;
    display: block;
    float: left;
}

.main-header__top-left:before {
    position: absolute;
    top: -8px;
    bottom: -7px;
    left: -10000000px;
    right: 100%;
    content: "";

    z-index: -1;
}

.main-header__top-left:after {
    position: absolute;
    top: -8px;
    bottom: -7px;
    left: 0px;
    right: 403px;
    content: "";

    z-index: -1;
}

.main-header__top-address {
    position: relative;
    display: flex;
    align-items: center;
}

.main-header__top-address li {
    position: relative;
    display: flex;
    align-items: center;
    line-height: 35px;
}

.main-header__top-address li+li {
    margin-left: 30px;
}

.main-header__top-address li .icon span {
    font-size: 14px;
    color: var(--sopot-pink);
}

.main-header__top-inner .mobile-nav__social a {
    color: var(--sopot-pink) !important;
}
.main-header__top-inner .mobile-nav__social a:hover {
    color: var(--sopot-white) !important;
}

.main-header__top-address li .text {
    margin-left: 10px;
}

.main-header__top-address li .text p {
    font-size: 14px;
    color: var(--sopot-pink);
    margin: 0;
}

.main-header__top-address li .text a {
    font-size: 14px;
    color: var(--sopot-pink);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.main-header__top-address li .text a:hover {
    color: #dddddd;
}

.main-header__top-right {
    position: relative;
    display: flex;
    float: right;
    align-items: center;
    padding: 9.5px 0;
}

.main-header__top-right-social {
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    padding: 10px 0;
}

.main-header__top-right-content {
    position: relative;
    display: flex;
    align-items: center;
}

.main-header__top-right-content li+li {
    margin-left: 20px;
}

.main-header__top-right-content li a {
    font-size: 14px;
    color: var(--sopot-white);
    display: flex;
    align-items: center;
    position: relative;
    letter-spacing: 0.015em;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.main-header__top-right-content li a:hover {
    color: var(--sopot-base);
}

.main-header__top-right-content li a i {
    padding-right: 7px;
}


.main-menu {
    position: relative;
    z-index: 91;
}

.main-menu-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.main-menu-wrapper__left {
    position: relative;
    display: block;
    flex: 1;
}

.main-menu-wrapper__center {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
}

.main-menu-wrapper__logo {
    position: relative;
    padding-top: 5px;
    padding-bottom: 0px;
}
.main-menu-wrapper__logo img{
    max-height:100px;
}
.main-menu-wrapper__right {
    position: relative;
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: flex-end;
}

.main-menu-wrapper__main-menu {
    position: relative;
    display: block;
    margin-right: 50px;
}

.stricky-header.main-menu {
    background-color: var(--sopot-white);
}

.main-menu .main-menu__list,
.main-menu .main-menu__list ul,
.stricky-header .main-menu__list,
.stricky-header .main-menu__list ul {
    margin: 0;
    padding: 0;
    list-style-type: none;
    align-items: center;
    display: none;
}

@media (min-width: 1200px) {

    .main-menu .main-menu__list,
    .main-menu .main-menu__list ul,
    .stricky-header .main-menu__list,
    .stricky-header .main-menu__list ul {
        display: flex;
    }
}

.main-menu .main-menu__list>li,
.stricky-header .main-menu__list>li {
    padding-top: 42px;
    padding-bottom: 42px;
    position: relative;
}

.main-menu .main-menu__list>li+li,
.stricky-header .main-menu__list>li+li {
    margin-left: 45px;
}

.main-menu .main-menu__list>li>a,
.stricky-header .main-menu__list>li>a {
    font-size: 16px;
    display: flex;
    align-items: center;
    color: var(--sopot-black);
    position: relative;
    transition: all 500ms ease;
    font-weight: 500;
}

.main-menu .main-menu__list>li>a::before,
.stricky-header .main-menu__list>li>a::before {
    content: "";
    height: 2px;
    border-radius: 0px;
    background-color: var(--sopot-base);
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    transition: transform 500ms ease;
    transform: scale(0, 1);
    transform-origin: left center;
    z-index: 1;
}

.main-menu .main-menu__list>li.current>a,
.main-menu .main-menu__list>li:hover>a,
.stricky-header .main-menu__list>li.current>a,
.stricky-header .main-menu__list>li:hover>a {
    color: var(--sopot-base);
}

.main-menu .main-menu__list>li.current>a::before,
.main-menu .main-menu__list>li:hover>a::before,
.stricky-header .main-menu__list>li.current>a::before,
.stricky-header .main-menu__list>li:hover>a::before {
    transform: scale(1, 1);
    transform-origin: right center;
}

.main-menu .main-menu__list>li.current>a::before {
    background-color: var(--sopot-base);
}

.main-menu .main-menu__list>li:hover>a::before {
    background-color: var(--sopot-base);
}

.main-menu .main-menu__list>li.dropdown>a:after {
    position: absolute;
    top: 1px;
    right: -16px;
    font-family: 'icomoon' !important;
    content: "\e93d";
    font-size: 12px;
    transition: transform 500ms ease;
    z-index: 1;
}

.main-menu .main-menu__list li ul,
.stricky-header .main-menu__list li ul {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 220px;
    background-color: #fff;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    opacity: 0;
    visibility: hidden;
    transition: 500ms ease;
    box-shadow: 0px 0px 65px 0px rgba(0, 0, 0, 0.1);
    -moz-transform: translateY(30px);
    -webkit-transform: translateY(30px);
    -ms-transform: translateY(30px);
    -o-transform: translateY(30px);
    transform: translateY(30px);
    z-index: 99;
}

.main-menu .main-menu__list li:hover>ul,
.stricky-header .main-menu__list li:hover>ul {
    opacity: 1;
    visibility: visible;
    -moz-transform: translateY(0);
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    transform: translateY(0);
}

.main-menu .main-menu__list li ul li,
.stricky-header .main-menu__list li ul li {
    flex: 1 1 100%;
    width: 100%;
    position: relative;
}

.main-menu .main-menu__list li ul li+li,
.stricky-header .main-menu__list li ul li+li {
    border-top: 1px solid RGBA(var(--sopot-black), 0.1);
}

.main-menu .main-menu__list li ul li a,
.stricky-header .main-menu__list li ul li a {
    font-size: 16px;
    line-height: 30px;
    color: var(--sopot-black);
    text-transform: capitalize;
    letter-spacing: 0;
    font-weight: 500;
    display: flex;
    padding-left: 20px;
    padding-right: 20px;
    padding-top: 10px;
    padding-bottom: 10px;
    transition: 500ms;
}

.main-menu .main-menu__list li ul li:hover>a,
.stricky-header .main-menu__list li ul li:hover>a {
    background-color: var(--sopot-base);
    color: var(--sopot-white);
}

.main-menu .main-menu__list li ul li>ul,
.stricky-header .main-menu__list li ul li>ul {
    top: 0;
    left: 100%;
}

.main-menu .main-menu__list li ul li>ul.right-align,
.stricky-header .main-menu__list li ul li>ul.right-align {
    top: 0;
    left: auto;
    right: 100%;
}

.main-menu .main-menu__list li ul li>ul ul,
.stricky-header .main-menu__list li ul li>ul ul {
    display: none;
}

.stricky-header {
    position: fixed;
    z-index: 991;
    top: 0;
    left: 0;
    background-color: #fff;
    width: 100%;
    visibility: hidden;
    transform: translateY(-120%);
    transition: transform 500ms ease, visibility 500ms ease;
    box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.05);
}

@media (max-width: 1199px) {
    .stricky-header {
        display: none !important;
    }
}

.stricky-header.stricky-fixed {
    transform: translateY(0);
    visibility: visible;
}

.stricky-header .main-menu__inner {
    box-shadow: none;
    padding-right: 0;
    max-width: 1170px;
    width: 100%;
    margin: 0 auto;
}

.mobile-nav__buttons {
    display: flex;
    margin-left: auto;
    margin-right: 10px;
}

@media (min-width: 1200px) {
    .mobile-nav__buttons {
        display: none;
    }
}

.mobile-nav__buttons a {
    font-size: 20px;
    color: var(--sopot-black);
    cursor: pointer;
}

.mobile-nav__buttons a+a {
    margin-left: 10px;
}

.mobile-nav__buttons a:hover {
    color: var(--sopot-base);
}

.main-menu .mobile-nav__toggler {
    font-size: 20px;
    color: var(--sopot-base);
    cursor: pointer;
    transition: 500ms;
}

.main-menu .mobile-nav__toggler:hover {
    color: var(--sopot-black);
}

@media (min-width: 1200px) {
    .main-menu .mobile-nav__toggler {
        display: none;
    }
}

/*--------------------------------------------------------------
# Navigations Two
--------------------------------------------------------------*/
.main-header-two {
    background: transparent;
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    transition: all 500ms ease;
    z-index: 999;
}

.main-header-two:before {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    content: "";
    height: 1px;
    background-color: rgba(var(--sopot-white-rgb), .10);
}

.main-menu-two {}

.main-menu-two .main-menu__list>li>a,
.stricky-header .main-menu-two .main-menu__list>li>a {
    color: var(--sopot-white);
}

.stricky-header.main-menu-two {
    background-color: var(--sopot-black);
}

/*--------------------------------------------------------------
# Navigations Three
--------------------------------------------------------------*/
.main-menu-three__wrapper {
    position: relative;
    display: block;
}

.main-menu-three__wrapper-logo {
    position: relative;
    float: left;
    padding-top: 36px;
    padding-bottom: 36px;
}

.main-menu-three__wrapper-main-menu {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 89%;
    float: left;
}

.main-menu-three__wrapper-right {
    position: relative;
    display: block;
    float: right;
    padding: 41px 0;
}

.main-menu-three__wrapper-search-cat {
    position: relative;
    display: flex;
    align-items: center;
}

.main-menu-three__wrapper-search-cat:before {
    position: absolute;
    top: -7px;
    bottom: -8px;
    left: -40px;
    content: "";
    background-color: rgba(var(--sopot-white-rgb), .10);
    width: 1px;
}

.main-menu-three__wrapper-search {
    font-size: 24px;
    color: var(--sopot-white);
    margin-right: 30px;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.main-menu-three__wrapper-search:hover {
    color: var(--sopot-base);
}

.main-menu-three__wrapper-cart {
    font-size: 24px;
    color: var(--sopot-white);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.main-menu-three__wrapper-cart:hover {
    color: var(--sopot-base);
}

.main-menu-three .main-menu__list>li.current>a::before {
    background-color: var(--sopot-white);
}

.main-menu-three .main-menu__list>li:hover>a::before {
    background-color: var(--sopot-white);
}


/*--------------------------------------------------------------
# Animations
--------------------------------------------------------------*/
@keyframes bubbleMover {
    0% {
        -webkit-transform: translateY(0px) translateX(0) rotate(0);
        transform: translateY(0px) translateX(0) rotate(0);
    }

    30% {
        -webkit-transform: translateY(30px) translateX(50px) rotate(15deg);
        transform: translateY(30px) translateX(50px) rotate(15deg);
        -webkit-transform-origin: center center;
        transform-origin: center center;
    }

    50% {
        -webkit-transform: translateY(50px) translateX(100px) rotate(45deg);
        transform: translateY(50px) translateX(100px) rotate(45deg);
        -webkit-transform-origin: right bottom;
        transform-origin: right bottom;
    }

    80% {
        -webkit-transform: translateY(30px) translateX(50px) rotate(15deg);
        transform: translateY(30px) translateX(50px) rotate(15deg);
        -webkit-transform-origin: left top;
        transform-origin: left top;
    }

    100% {
        -webkit-transform: translateY(0px) translateX(0) rotate(0);
        transform: translateY(0px) translateX(0) rotate(0);
        -webkit-transform-origin: center center;
        transform-origin: center center;
    }
}

@keyframes shapeMover {

    0%,
    100% {
        transform: perspective(400px) translateY(0) rotate(0deg) translateZ(0px) translateX(0);
    }

    50% {
        transform: perspective(400px) rotate(0deg) translateZ(20px) translateY(20px) translateX(20px);
    }
}

@keyframes banner3Shake {
    0% {
        -webkit-transform: rotate3d(0, 1, 0, 0deg);
        transform: rotate3d(0, 1, 0, 0deg);
    }

    30% {
        -webkit-transform: rotate3d(0, 0, 1, 5deg);
        transform: rotate3d(0, 0, 1, 5deg);
    }

    60% {
        -webkit-transform: rotate3d(1, 0, 0, 0deg);
        transform: rotate3d(1, 0, 0, 0deg);
    }

    80% {
        -webkit-transform: rotate3d(0, 0, 1, 5deg);
        transform: rotate3d(0, 0, 1, 5deg);
    }

    100% {
        -webkit-transform: rotate3d(0, 1, 0, 0deg);
        transform: rotate3d(0, 1, 0, 0deg);
    }
}

@keyframes squareMover {

    0%,
    100% {
        -webkit-transform: translate(0, 0) rotate(0);
        transform: translate(0, 0) rotate(0);
    }

    20%,
    60% {
        -webkit-transform: translate(20px, 40px) rotate(180deg);
        transform: translate(20px, 40px) rotate(180deg);
    }

    30%,
    80% {
        -webkit-transform: translate(40px, 60px) rotate(0deg);
        transform: translate(40px, 60px) rotate(0deg);
    }
}

@keyframes treeMove {

    0%,
    100% {
        -webkit-transform: rotate(0deg) translateX(0);
        transform: rotate(0deg) translateX(0);
    }

    25%,
    75% {
        -webkit-transform: rotate(5deg) translateX(15px);
        transform: rotate(5deg) translateX(15px);
    }

    50% {
        -webkit-transform: rotate(10deg) translateX(30px);
        transform: rotate(10deg) translateX(30px);
    }
}

/*--------------------------------------------------------------
#     Zoom Fade Css
--------------------------------------------------------------*/
.zoom-fade {
    animation-name: zoom-fade;
    animation-duration: 5s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;

    -webkit-animation-name: zoom-fade;
    -webkit-animation-duration: 5s;
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-timing-function: linear;

    -moz-animation-name: zoom-fade;
    -moz-animation-duration: 5s;
    -moz-animation-iteration-count: infinite;
    -moz-animation-timing-function: linear;

    -ms-animation-name: zoom-fade;
    -ms-animation-duration: 5s;
    -ms-animation-iteration-count: infinite;
    -ms-animation-timing-function: linear;

    -o-animation-name: zoom-fade;
    -o-animation-duration: 5s;
    -o-animation-iteration-count: infinite;
    -o-animation-timing-function: linear;
}

@-webkit-keyframes zoom-fade {
    0% {
        -webkit-transform: scale(0.9);
        transform: scale(0.9);
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }

    100% {
        -webkit-transform: scale(0.9);
        transform: scale(0.9);
    }
}

@keyframes zoom-fade {
    0% {
        -webkit-transform: scale(0.9);
        transform: scale(0.9);
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }

    100% {
        -webkit-transform: scale(0.9);
        transform: scale(0.9);
    }
}

@-moz-keyframes service_hexagon_2 {
    0% {
        -moz-transform: rotateY(0deg);
        transform: rotateY(0deg);
    }

    100% {
        -moz-transform: rotateY(360deg);
        transform: rotateY(360deg);
    }
}

@-o-keyframes service_hexagon_2 {
    0% {
        -o-transform: rotateY(0deg);
        transform: rotateY(0deg);
    }

    100% {
        -o-transform: rotateY(360deg);
        transform: rotateY(360deg);
    }
}

@keyframes service_hexagon_2 {
    0% {
        -webkit-transform: rotateY(0deg);
        -moz-transform: rotateY(0deg);
        -o-transform: rotateY(0deg);
        transform: rotateY(0deg);
    }

    100% {
        -webkit-transform: rotateY(360deg);
        -moz-transform: rotateY(360deg);
        -o-transform: rotateY(360deg);
        transform: rotateY(360deg);
    }
}

/*--------------------------------------------------------------
# Mobile Nav
--------------------------------------------------------------*/
.mobile-nav__wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    opacity: 0;
    transform: translateX(-50%);
    transform-origin: right center;
    transition: transform 500ms ease-in, opacity 500ms linear,
        visibility 500ms ease-in;
    z-index: 999;
    visibility: hidden;
}

.mobile-nav__wrapper .container {
    padding-left: 0;
    padding-right: 0;
}

.mobile-nav__wrapper.expanded {
    opacity: 1;
    transform: translateX(0%);
    visibility: visible;
}

.mobile-nav__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #000000;
    opacity: 0.5;
    cursor: pointer;
}

.mobile-nav__content {
    width: 300px;
    background-color: var(--sopot-black);
    z-index: 10;
    position: relative;
    height: 100%;
    overflow-y: auto;
    padding-top: 30px;
    padding-bottom: 30px;
    padding-left: 15px;
    padding-right: 15px;
}

.mobile-nav__content .sopot-btn {
    padding: 8px 0;
    width: 100%;
    text-align: center;
}

.mobile-nav__content .logo-box {
    margin-bottom: 40px;
    display: flex;
    justify-content: center;

}

.mobile-nav__close {
    position: absolute;
    top: 20px;
    right: 15px;
    font-size: 18px;
    color: var(--sopot-text-dark);
    cursor: pointer;
}

.mobile-nav__content .main-menu__list,
.mobile-nav__content .main-menu__list ul {
    margin: 0;
    padding: 0;
    list-style-type: none;
}

.mobile-nav__content .main-menu__list ul {
    display: none;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-nav__content .main-menu__list li:not(:last-child) {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-nav__content .main-menu__list li a {
    display: flex;
    justify-content: space-between;
    line-height: 30px;
    color: #ffffff;
    font-size: 14px;
    font-family: var(--sopot-font);
    font-weight: 500;
    height: 46px;
    align-items: center;
    transition: 500ms;
}

.mobile-nav__content .main-menu__list li a.expanded {
    color: var(--sopot-base);
}

.mobile-nav__content .main-menu__list li a button {
    width: 30px;
    height: 30px;
    background-color: var(--sopot-base);
    border: none;
    outline: none;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    transform: rotate(-90deg);
    transition: transform 500ms ease;
}

.mobile-nav__content .main-menu__list li a button.expanded {
    transform: rotate(0deg);
    background-color: #fff;
    color: var(--sopot-black);
}

.mobile-nav__content .main-menu__list li.cart-btn span {
    position: relative;
    top: auto;
    right: auto;
    transform: translate(0, 0);
}

.mobile-nav__content .main-menu__list li.cart-btn i {
    font-size: 16px;
}

.mobile-nav__top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
}

.mobile-nav__top .main-menu__login a {
    color: var(--sopot-text-dark);
}

.mobile-nav__container {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-nav__social {
    display: flex;
    align-items: center;
}

.mobile-nav__social a {
    font-size: 16px;
    color: var(--sopot-white);
    transition: 500ms;
}

.mobile-nav__social a+a {
    margin-left: 30px;
}

.mobile-nav__social a:hover {
    color: var(--sopot-base);
}

.mobile-nav__contact {
    margin-bottom: 0;
    margin-top: 20px;
    margin-bottom: 20px;
}

.mobile-nav__contact li {
    color: var(--sopot-text-dark);
    font-size: 14px;
    font-weight: 500;
    position: relative;
    display: flex;
    align-items: center;
}

.mobile-nav__contact li+li {
    margin-top: 15px;
}

.mobile-nav__contact li a {
    color: #ffffff;
    transition: 500ms;
}

.mobile-nav__contact li a:hover {
    color: var(--sopot-base);
}

.mobile-nav__contact li>i {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: var(--sopot-base);
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    font-size: 11px;
    margin-right: 10px;
    color: #fff;
}

.mobile-nav__container .main-logo,
.mobile-nav__container .topbar__buttons,
.mobile-nav__container .main-menu__language,
.mobile-nav__container .main-menu__login {
    display: none;
}

/*--------------------------------------------------------------
# Search Popup
--------------------------------------------------------------*/
.search-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    padding-left: 20px;
    padding-right: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    transform: translateY(-110%);
    transition: transform 500ms ease, opacity 500ms ease;
}

.search-popup.active {
    transform: translateY(0%);
}

.search-popup__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--sopot-black);
    opacity: 0.75;
    cursor: pointer;
}

.search-popup__content {
    width: 100%;
    max-width: 560px;
}

.search-popup__content form {
    display: flex;
    flex-wrap: wrap;
    position: relative;
    background-color: #fff;
    border-radius: 0px;
    overflow: hidden;
}

.search-popup__content form input[type="search"],
.search-popup__content form input[type="text"] {
    width: 100%;
    background-color: #fff;
    font-size: 16px;
    border: none;
    outline: none;
    height: 66px;
    padding-left: 30px;
}

.search-popup__content .thm-btn {
    padding: 0;
    width: 68px;
    height: 68px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    position: absolute;
    top: 0;
    right: -1px;
    border-radius: 0;
    background-color: var(--sopot-base);
    border: 0;
}

.search-popup__content .thm-btn:hover {
    background-color: var(--sopot-black);
}

.search-popup__content .thm-btn i {
    margin: 0;
}

/*--------------------------------------------------------------
# Main Slider
--------------------------------------------------------------*/
.main-slider {
    overflow: hidden;
    position: relative;
}

.main-slider .swiper-slide {
    position: relative;
    background-color: var(--sopot-black);
}

.main-slider .container {
    position: relative;
    padding-top: 243px;
    padding-bottom: 255px;
    z-index: 30;
}

.main-slider .image-layer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    transform: scale(1);
    transition: transform 7000ms ease, opacity 1500ms ease-in;
    opacity: .7;
    background-color: var(--sopot-black);
    z-index: 1;
}

.main-slider .swiper-slide-active .image-layer {
    transform: scale(1.15);
}

.main-slider-shape-1 {
    position: absolute;
    top: 155px;
    left: 280px;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-500px) rotate(0deg);
    transition-delay: 1000ms;
    transition: transform 2000ms ease, opacity 2000ms ease;
}

.main-slider .swiper-slide-active .main-slider-shape-1 {
    visibility: visible;
    opacity: 1;
    transform: translateY(0) translateX(0) rotate(75deg);
}

.main-slider-shape-1:before,
.main-slider-shape-1:after {
    content: "";
    position: absolute;
    width: 0;
    border-left: 40px solid transparent;
    border-right: 40px solid transparent;
    left: 0px;
    right: 0px;
    opacity: .8;
}

.main-slider-shape-1:before {
    bottom: 100%;
    border-bottom: 70px solid var(--sopot-base);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.main-slider-shape-2 {
    position: absolute;
    bottom: 220px;
    right: 350px;
    opacity: 0;
    visibility: hidden;
    transform: translateX(900px) rotate(0deg);
    transition-delay: 1500ms;
    transition: transform 2000ms ease, opacity 2000ms ease;
}

.main-slider .swiper-slide-active .main-slider-shape-2 {
    visibility: visible;
    opacity: 1;
    transform: translateY(0) translateX(0) rotate(100deg);
}

.main-slider-shape-2:before,
.main-slider-shape-2:after {
    content: "";
    position: absolute;
    width: 0;
    border-left: 40px solid transparent;
    border-right: 40px solid transparent;
    left: 0px;
    right: 0px;
    opacity: .8;
}

.main-slider-shape-2:before {
    bottom: 100%;
    border-bottom: 70px solid var(--sopot-white);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.main-slider-shape-3 {
    position: absolute;
    left: 85px;
    top: 60px;
    border-radius: 50%;
    background-color: rgba(var(--sopot-base-rgb), 1);
    width: 24px;
    height: 24px;
}

.main-slider-shape-4 {
    position: absolute;
    left: 1075px;
    bottom: 220px;
    border-radius: 50%;
    background-color: rgba(var(--sopot-base-rgb), 1);
    width: 24px;
    height: 24px;
}

.main-slider-shape-5 {
    position: absolute;
    top: 100px;
    right: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-900px) rotate(0deg);
    transition-delay: 1500ms;
    transition: transform 2000ms ease, opacity 2000ms ease;
}

.main-slider .swiper-slide-active .main-slider-shape-5 {
    visibility: visible;
    opacity: 1;
    transform: translateY(0) translateX(0) rotate(0deg);
}

.main-slider-shape-5 img {
    width: auto;
}

.main-slider__content {
    position: relative;
    display: block;
    overflow: hidden;
}

.main-slider h2 {
    font-size: 60px;
    color: var(--sopot-white);
    font-weight: 700;
    line-height: 71px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-220px);
    transition-delay: 1000ms;
    transition: transform 2000ms ease, opacity 2000ms ease;
}

.main-slider h2 span {
    position: relative;
    display: inline-block;
}

.main-slider h2 span:before {
    position: absolute;
    left: -15px;
    bottom: -6px;
    width: 200px;
    height: 23px;
    content: "";
    background-image: url(../images/shapes/main-slider-circle.png);
    background-repeat: no-repeat;
    background-size: cover;
}

.main-slider p {
    font-size: 16px;
    color: var(--sopot-white);
    margin: 0;
    letter-spacing: 0.002em;
    opacity: 0;
    transform: translateY(120px);
    transition-delay: 2500ms;
    transition: transform 2000ms ease,
        opacity 2000ms ease,
        color 500ms ease,
        background 500ms ease;
    padding-top: 20px;
    padding-bottom: 42px;
}

.main-slider__btn {
    opacity: 0;
    transform: translateY(-220px);
    transition-delay: 4000ms;
    transition: transform 2000ms ease,
        opacity 2000ms ease,
        color 500ms ease,
        background 500ms ease;
}

.swiper-slide-active .main-slider__btn,
.main-slider .swiper-slide-active h2,
.main-slider .swiper-slide-active p {
    visibility: visible;
    opacity: 1;
    transform: translateY(0) translateX(0);
}

.main-slider-arrow {
    position: absolute;
    bottom: 19px;
    right: 283px;
    transform: translateX(100%);
    opacity: 0;
}

.main-slider .swiper-slide-active .main-slider-arrow {
    opacity: 1;
    transform: translate(0);
    transition: all 1000ms ease;
    transition-delay: 200ms;
}

#main-slider-pagination {
    z-index: 10;
    bottom: 176px;
    left: 50%;
    width: 100%;
    max-width: 1170px;
    -webkit-transform: translate(-50%, calc(-50% + 95px));
    transform: translate(-50%, calc(0% + 0px));
    text-align: left;
}

#main-slider-pagination .swiper-pagination-bullet {
    margin: 0;
}

#main-slider-pagination .swiper-pagination-bullet {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--sopot-white);
    opacity: .5;
    -webkit-transition: all 300ms ease;
    transition: all 300ms ease;
}

#main-slider-pagination .swiper-pagination-bullet+.swiper-pagination-bullet {
    margin-left: 10px;
}

#main-slider-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
    opacity: 1;
}

.main-slider__nav {
    width: 100%;
    max-width: 1200px;
    padding-left: 15px;
    padding-right: 15px;
    position: absolute;
    top: 54%;
    left: 50%;
    z-index: 100;
    transform: translateY(-50%) translateX(-50%);
    display: flex;
    align-items: flex-end;
    flex-direction: column;
}


.main-slider__nav .swiper-button-next,
.main-slider__nav .swiper-button-prev {
    position: relative;
    top: auto;
    left: auto;
    right: auto;
    bottom: auto;
    z-index: 100;
    width: 60px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    color: var(--sopot-white);
    background-color: transparent;
    border: 2px solid rgba(var(--sopot-white-rgb), .30);
    border-radius: 0;
    opacity: .3;
    margin: 0;
    text-align: center;
    transition: all 500ms ease;
}

.main-slider__nav .swiper-button-next:hover,
.main-slider__nav .swiper-button-prev:hover {
    opacity: 1;
    border: 2px solid rgba(var(--sopot-white-rgb), 1);
}

.main-slider__nav .swiper-button-next::after,
.main-slider__nav .swiper-button-prev::after {
    display: none;
}

.main-slider__nav .swiper-button-prev {
    margin-bottom: 10px;
}

/*--------------------------------------------------------------
# Main Slider Two
--------------------------------------------------------------*/
.main-slider-two {
    position: relative;
    display: block;
}

.main-slider-two .container {
    position: relative;
    padding-top: 278px;
    padding-bottom: 240px;
}

.main-slider-two__map {
    position: absolute;
    bottom: 60px;
    left: 0;
    opacity: .4;
    transform: translateX(-100%);
    mix-blend-mode: soft-light;
    z-index: -1;
}

.main-slider-two__map img {
    width: auto;
}

.main-slider-two .swiper-slide-active .main-slider-two__map {
    opacity: .4;
    transform: translateX(0px);
    transition: all 1500ms ease;
}

.main-slider-two__img {
    position: absolute;
    bottom: 109px;
    right: 405px;
    transform: translateX(100%);
    z-index: 1;
}

.main-slider-two__img img {
    width: auto;
}

.swiper-slide-active .main-slider-two__img {
    opacity: 1;
    transform: translateX(0px);
    transition: transform 2000ms ease, opacity 2000ms ease, color 500ms ease,
        background 500ms ease;
}

.main-slider-two__img:before {
    position: absolute;
    top: 20px;
    right: -20px;
    left: 20px;
    bottom: -20px;
    content: "";
    background-color: var(--sopot-base);
    z-index: -1;
}

.main-slider__nav-two {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}


.main-slider__nav-two .swiper-button-next {
    position: relative;
    width: 60px;
    height: 50px;
    line-height: 60px;
    border-radius: 0;
    text-align: center;
    color: var(--sopot-white);
    cursor: pointer;
    z-index: 9;
    margin-right: 100px;
    font-size: 20px;
    border: 2px solid rgba(var(--sopot-white-rgb), .30);
    background-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: .3;
    transition: all 500ms ease;
}

.main-slider__nav-two .swiper-button-prev {
    position: relative;
    width: 60px;
    height: 50px;
    line-height: 60px;
    border-radius: 0;
    text-align: center;
    color: var(--sopot-white);
    cursor: pointer;
    z-index: 9;
    margin-left: 100px;
    font-size: 20px;
    border: 2px solid rgba(var(--sopot-white-rgb), .30);
    background-color: transparent;
    opacity: .30;
    transition: all 500ms ease;
    display: flex;
    align-items: center;
    justify-content: center;
}


.main-slider__nav-two .swiper-button-next:hover,
.main-slider__nav-two .swiper-button-prev:hover {
    opacity: 1;
    border: 2px solid rgba(var(--sopot-white-rgb), 1);
}

.main-slider__nav-two .swiper-button-next::after,
.main-slider__nav-two .swiper-button-prev::after {
    display: none;
}

.main-slider__nav-two .swiper-button-prev {
    margin-bottom: 10px;
}

/*--------------------------------------------------------------
# Main Slider Three
--------------------------------------------------------------*/
.main-slider-three {
    position: relative;
    display: block;
}

.main-slider-three .container {
    padding-top: 189px;
    padding-bottom: 200px;
}

.main-slider-three .main-slider__content {
    text-align: center;
}

.main-slider__three-map {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
    width: 583px;
    height: 410px;
    background-repeat: no-repeat;
    background-position: center;
    mix-blend-mode: luminosity;
    opacity: 0.08;
    z-index: -1;
}

.main-slider__three-map img {
    width: auto;
}

.main-slider__nav-three {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}


.main-slider__nav-three .swiper-button-next {
    position: relative;
    width: 60px;
    height: 50px;
    line-height: 60px;
    border-radius: 0;
    text-align: center;
    color: var(--sopot-white);
    cursor: pointer;
    z-index: 9;
    margin-right: 100px;
    font-size: 20px;
    border: 2px solid rgba(var(--sopot-white-rgb), .30);
    background-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: .3;
    transition: all 500ms ease;
}

.main-slider__nav-three .swiper-button-prev {
    position: relative;
    width: 60px;
    height: 50px;
    line-height: 60px;
    border-radius: 0;
    text-align: center;
    color: var(--sopot-white);
    cursor: pointer;
    z-index: 9;
    margin-left: 100px;
    font-size: 20px;
    border: 2px solid rgba(var(--sopot-white-rgb), .30);
    background-color: transparent;
    opacity: .30;
    transition: all 500ms ease;
    display: flex;
    align-items: center;
    justify-content: center;
}


.main-slider__nav-three .swiper-button-next:hover,
.main-slider__nav-three .swiper-button-prev:hover {
    opacity: 1;
    border: 2px solid rgba(var(--sopot-white-rgb), 1);
}

.main-slider__nav-three .swiper-button-next::after,
.main-slider__nav-three .swiper-button-prev::after {
    display: none;
}

.main-slider__nav-three .swiper-button-prev {
    margin-bottom: 10px;
}


.main-slider-three #main-slider-pagination {
    text-align: center;
    bottom: 121px;
}




/***
=============================================
    Float Bob X Animation Css
=============================================
***/
@-webkit-keyframes float-bob-x {
    0% {
        transform: translateX(-30px);
    }

    50% {
        transform: translateX(-10px);
    }

    100% {
        transform: translateX(-30px);
    }
}

@keyframes float-bob-x {
    0% {
        transform: translateX(-30px);
    }

    50% {
        transform: translateX(-10px);
    }

    100% {
        transform: translateX(-30px);
    }
}

.float-bob-x {
    -webkit-animation-name: float-bob-x;
    animation-name: float-bob-x;
    -webkit-animation-duration: 2s;
    animation-duration: 2s;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
}

/*--------------------------------------------------------------
# Feature One
--------------------------------------------------------------*/
.feature-one {
    position: relative;
    display: block;
    padding: 120px 0 100px;
    margin-top: 0px;
}

.feature-one .row {
    --bs-gutter-x: 0px;
}

.feature-one__single {
    position: relative;
    display: block;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 0px 35px 0px rgba(0, 0, 0, 0.06);
    padding: 42px 40px 38px;
    margin-bottom: 30px;
    z-index: 1;
}

.feature-one__single:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    background-color: var(--sopot-base);
    transition-delay: .1s;
    transition-timing-function: ease-in-out;
    transition-duration: .5s;
    transition-property: all;
    opacity: 1;
    transform-origin: bottom;
    transform-style: preserve-3d;
    transform: scaleY(0);
    z-index: -1;
}

.feature-one__single:hover:before {
    transform: scaleY(1.0);
}

.feature-one__top {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 24px;
}

.feature-one__icon {
    position: relative;
    display: flex;
    align-items: center;
}

.feature-one__icon span {
    font-size: 45px;
    color: var(--sopot-base);
    position: relative;
    display: inline-block;
    transition: all 500ms linear;
    transition-delay: 0.1s;
    transform: scale(1);
}

.feature-one__single:hover .feature-one__icon span {
    color: var(--sopot-white);
    transform: scale(1.2);
}

.feature-one__icon:before {
    position: absolute;
    top: 0;
    right: -5px;
    height: 35px;
    width: 35px;
    background-color: rgba(var(--sopot-black-rgb), .15);
    content: "";
    border-radius: 50%;
    transition: all 500ms linear;
    transition-delay: 0.1s;
    transform: scale(1);
}

.feature-one__single:hover .feature-one__icon:before {
    transform: scale(.9);
    background-color: rgba(var(--sopot-black-rgb), .99);
}

.feature-one__title {
    position: relative;
    display: inline-block;
    font-size: 25px;
    line-height: 25px;
    font-weight: 500;
    margin-left: 20px;
}

.feature-one__title:before {
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    content: "";
    background-color: var(--sopot-white);
    height: 1px;
    transform: scaleX(0.0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.62, 0.25, 0.45, 0.22);
    transition-delay: 0.3s;
}

.feature-one__single:hover .feature-one__title:before {
    transform: scaleX(1.0);
}

.feature-one__title a {
    color: var(--sopot-black);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.feature-one__single:hover .feature-one__title a {
    color: var(--sopot-white);
}

.feature-one__text {
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.feature-one__single:hover .feature-one__text {
    color: var(--sopot-white);
}

/*--------------------------------------------------------------
# About One 
--------------------------------------------------------------*/
.about-one {
    position: relative;
    display: block;
    padding: 0 0 120px;
}

.about-one.pd-120-0-120 {
    padding: 120px 0 120px;
}

.about-one__left {
    position: relative;
    display: block;
    margin-right: 57px;
    z-index: 1;
}

.about-one__left:before {
    position: absolute;
    bottom: -53px;
    left: 0;
    content: "";
    background-color: var(--sopot-base);
    z-index: -1;
    height: 10px;
    width: 200px;
}

.about-one__left:after {
    position: absolute;
    bottom: -77px;
    left: 0;
    content: "";
    background-color: var(--sopot-black);
    z-index: -1;
    height: 5px;
    width: 200px;
}

.about-one__img-box {
    position: relative;
    display: block;
    z-index: 1;
}

.about-one__img-box:after {
    position: absolute;
    top: -10px;
    bottom: 0;
    right: -10px;
    left: 10px;
    content: "";
    background-color: var(--sopot-base);
    z-index: -1;
}

.about-one__img-box:before {
    position: absolute;
    bottom: -25px;
    left: 0;
    content: "";
    background-color: var(--sopot-black);
    z-index: -1;
    height: 5px;
    width: 200px;
}

.about-one__img {
    position: relative;
    display: block;
    overflow: hidden;
    z-index: 1;
}

.about-one__img::before {
    position: absolute;
    top: 0%;
    left: 0%;
    display: block;
    content: '';
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, .4);
    border-radius: 50%;
    -webkit-transform: translateX(-50%, -50%);
    transform: translateX(-50%, -50%);
    opacity: 0;
    z-index: 1;
}

.about-one__img:hover:before {
    -webkit-animation: circle .95s;
    animation: circle .95s;
}

.about-one__img img {
    width: 100%;
}

.about-one__small-img {
    position: absolute;
    bottom: -155px;
    right: -50px;
    z-index: 2;
}

.about-one__small-img:after {
    position: absolute;
    top: -10px;
    bottom: 0;
    right: 0;
    left: -10px;
    content: "";
    background-color: var(--sopot-white);
    z-index: -1;
}

.about-one__small-img img {
    width: auto;
}

.about-one__right {
    position: relative;
    display: block;
    margin-top: -10px;
}

.about-one__right-content {
    position: relative;
    display: block;
    border: 1px dashed var(--sopot-base);
    padding: 75px 55px 63px;
}

.about-one__right-content .section-title {
    margin-bottom: 18px;
}

.about-one__points {
    position: relative;
    display: block;
    margin-top: 23px;
    margin-bottom: 29px;
}

.about-one__points li {
    position: relative;
    display: block;
    padding-left: 35px;
}

.about-one__points li+li {
    margin-top: 19px;
}

.about-one__points li .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 25px;
    width: 25px;
    background-color: var(--sopot-black);
    border-radius: 50%;
    font-size: 13px;
    color: var(--sopot-white);
    position: absolute;
    top: 5px;
    left: 0;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.about-one__points li:hover .icon {
    background-color: var(--sopot-base);
}

.about-one__points li .text h4 {
    font-size: 17px;
    font-weight: 500;
    line-height: 27px;
    margin-bottom: 3px;
}

.about-one__points li .text p {
    font-size: 14px;
    line-height: 20px;
}

.about-one__bottom-video-box {
    position: relative;
    display: flex;
    align-items: center;
}

.about-one__video-link {
    position: relative;
    display: block;
    z-index: 2;
    margin-left: 60px;
}

.about-one__video-icon {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    font-size: 15px;
    color: var(--sopot-white);
    background-color: var(--sopot-black);
    border-radius: 50%;
    margin: 0 auto;
    transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -webkit-transition: all 0.5s ease;
    -ms-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
}

.about-one__video-icon:hover {
    background-color: var(--sopot-base);
    color: var(--sopot-white);
}

.about-one__video-icon:before {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    content: "";
    border-radius: 50%;
    z-index: -1;
    background-color: var(--sopot-black);
    opacity: 0.52;
}

.about-one__video-icon:after {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    content: "";
    border-radius: 50%;
    z-index: -1;
    background-color: var(--sopot-black);
    opacity: 0.43;
}

.about-one__video-link .ripple,
.about-one__video-icon .ripple:before,
.about-one__video-icon .ripple:after {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 75px;
    height: 75px;
    transform: translate(-50%, -50%);
    -ms-box-shadow: 0 0 0 0 rgba(var(--sopot-base-rgb), 0.6);
    -o-box-shadow: 0 0 0 0 rgba(var(--sopot-base-rgb), 0.6);
    box-shadow: 0 0 0 0 rgba(var(--sopot-base-rgb), 0.6);
    -webkit-animation: ripple 3s infinite;
    animation: ripple 3s infinite;
    border-radius: 50%;
}

.about-one__video-icon .ripple:before {
    -webkit-animation-delay: 0.9s;
    animation-delay: 0.9s;
    content: "";
    position: absolute;
}

.about-one__video-icon .ripple:after {
    -webkit-animation-delay: 0.6s;
    animation-delay: 0.6s;
    content: "";
    position: absolute;
}

/*--------------------------------------------------------------
# Causes One
--------------------------------------------------------------*/
.causes-one {
    position: relative;
    display: block;
    background-color: #f8f8f9;
    padding: 120px 0 90px;
    z-index: 1;
}

.causes-one:after {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    content: "";
    background-color: rgba(var(--sopot-base-rgb), .10);
    height: 584px;
    z-index: -1;
}

.causes-one__single {
    position: relative;
    display: block;
    margin-bottom: 30px;
}

.causes-one__img {
    position: relative;
    display: block;
    overflow: hidden;
    z-index: 1;
}

.causes-one__img:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    background-color: rgba(var(--sopot-black-rgb), 0.5);
    transform: scaleY(0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.5s cubic-bezier(0.62, 0.21, 0.45, 1.52);
    z-index: 1;
}

.causes-one__single:hover .causes-one__img:before {
    transform: scaleY(1);
}

.causes-one__img img {
    width: 100%;
    transition-delay: .1s;
    transition-timing-function: ease-in-out;
    transition-duration: .7s;
    transition-property: all;
}

.causes-one__single:hover .causes-one__img img {
    transform: scale(1.1) rotate(2deg);
}

.causes-one__content-box {
    position: relative;
    display: block;
    background-color: var(--sopot-white);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.causes-one__single:hover .causes-one__content-box {
    box-shadow: 0px 0px 48.51px 0.49px rgba(0, 0, 0, 0.2);
}

.causes-one__donate-btn-box {
    position: absolute;
    top: -40px;
    right: 0;
}

.causes-one__donate-btn {
    padding: 8px 30px 6px;
    font-size: 14px;
    text-transform: uppercase;
    border-radius: 0;
}

.causes-one__content {
    position: relative;
    display: block;
    padding: 32px 25px 0px;
}

.causes-one__date {
    position: relative;
    display: flex;
    align-items: center;
}

.causes-one__date span {
    height: 45px;
    width: 45px;
    background-color: var(--sopot-base);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: 500;
    color: var(--sopot-white);
}

.causes-one__date h4 {
    font-size: 18px;
    font-weight: 500;
    font-family: var(--sopot-font);
    text-transform: uppercase;
    line-height: 24px;
    margin-left: 15px;
}

.causes-one__title {
    font-size: 22px;
    line-height: 32px;
    margin-bottom: 31px;
    font-weight: 600;
}

.causes-one__title a {
    color: var(--sopot-black);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.causes-one__title a:hover {
    color: var(--sopot-base);
}

.causes-one__progress {
    position: relative;
    display: block;
    width: 100%;
}

.causes-one__progress .bar {
    position: relative;
    width: 100%;
    height: 4px;
    background: rgba(var(--sopot-base-rgb), .10);
    border-radius: 0px;
    margin-bottom: 13px;
}

.causes-one__progress .bar-inner {
    position: relative;
    display: block;
    width: 0px;
    height: 4px;
    border-radius: 0px;
    background: var(--sopot-base);
    -webkit-transition: all 1500ms ease;
    -ms-transition: all 1500ms ease;
    -o-transition: all 1500ms ease;
    -moz-transition: all 1500ms ease;
    transition: all 1500ms ease;
}

.causes-one__progress .count-text {
    position: absolute;
    right: -40px;
    bottom: 11px;
    color: var(--sopot-white);
    line-height: 20px;
    font-size: 10px;
    text-align: center;
    font-weight: 400;
    opacity: 0;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
    -moz-transition: all 500ms ease;
    transition: all 500ms ease;
    height: 20px;
    width: 40px;
    background: var(--sopot-base);
}

.causes-one__progress .bar-inner.counted .count-text {
    opacity: 1;
}

.causes-one__progress .count-text:after {
    content: '';
    position: absolute;
    left: 0;
    top: 100%;
    border-right: 6px solid transparent;
    border-top: 4px solid var(--sopot-base);
}

.causes-one__goals {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.causes-one__goals>p {
    font-size: 14px;
    margin: 0;
}

.causes-one__goals>p>span {
    font-size: 18px;
    color: var(--sopot-black);
    font-weight: 500;
    line-height: 28px;
}

.causes-one__btn-box {
    position: relative;
    display: flex;
    align-items: center;
    margin-top: 18px;
}

.causes-one__read-more {
    font-size: 16px;
    color: var(--sopot-base);
    font-weight: 500;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
    position: relative;
    display: flex;
    align-items: center;
    padding: 7px 20px;
    z-index: 1;
}

.causes-one__single:hover:hover .causes-one__read-more {
    color: var(--sopot-white);
}

.causes-one__read-more:before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    content: "";
    background-color: rgba(var(--sopot-base-rgb), .20);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
    z-index: -1;
}

.causes-one__single:hover:hover .causes-one__read-more:before {
    background-color: rgba(var(--sopot-base-rgb), 1);
}

.causes-one__read-more span {
    padding-left: 5px;
}

.causes-one__bottom {
    position: relative;
    display: block;
    border-top: 1px solid #e9e9e9;
    padding-top: 15px;
    margin-top: 25px;
    padding-bottom: 20px;
}

.causes-one__list {
    position: relative;
    display: flex;
    align-items: center;
    padding: 0 25px 0;
}

.causes-one__list li {
    position: relative;
    display: flex;
    align-items: center;
}

.causes-one__list li+li {
    margin-left: 20px;
}

.causes-one__list li .icon {
    position: relative;
    display: flex;
    align-items: center;
}

.causes-one__list li .icon span {
    color: var(--sopot-base);
    font-size: 17px;
}

.causes-one__list li .text {
    margin-left: 10px;
}

.causes-one__list li .text p {
    font-size: 14px;
    line-height: 14px;
    letter-spacing: 0.015em;
}

/*--------------------------------------------------------------
# Porject One
--------------------------------------------------------------*/
.project-one {
    position: relative;
    display: block;
    padding: 120px 0 90px;
}

.project-one.pdt0 {
    padding-top: 0;
}

.project-one__single {
    position: relative;
    display: block;
    margin-bottom: 30px;
    padding: 30px;
}

.project-one__img {
    position: relative;
    display: block;
    overflow: hidden;
}

.project-one__img:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    background-color: rgba(var(--sopot-base-rgb), .80);
    width: 0%;
    transform: translateY(100%);
    transition: all 500ms ease;
    z-index: 1;
}

.project-one__single:hover .project-one__img:before {
    transform: translateY(0);
    width: 100%;
}

.project-one__img img {
    width: 100%;
    object-fit: contain;
    max-height: 125px;
    transition-delay: .1s;
    transition-timing-function: ease-in-out;
    transition-duration: .7s;
    transition-property: all;
}

.project-one__single:hover .project-one__img img {
    transform: scale(1.1) rotate(2deg);
}

.project-one__content {
    position: absolute;
    left: 20px;
    bottom: 20px;
    transform: scaleY(0.0);
    transform-origin: center;
    transform-style: preserve-3d;
    -webkit-transition: all 0.4s linear;
    -o-transition: all 0.4s linear;
    transition: all 0.4s linear;
    transform-origin: bottom center;
    z-index: 2;
}

.project-one__single:hover .project-one__content {
    transform: scaleY(1.0);
    -webkit-transition-delay: 500ms;
    -moz-transition-delay: 500ms;
    -ms-transition-delay: 500ms;
    -o-transition-delay: 500ms;
    transition-delay: 500ms;
}

.project-one__sub-title {
    font-size: 14px;
    color: var(--sopot-white);
}

.project-one__title {
    font-size: 25px;
    line-height: 35px;
}

.project-one__title a {
    color: var(--sopot-white);
}

/*--------------------------------------------------------------
# Brand One
--------------------------------------------------------------*/
.brand-one {
    position: relative;
    display: block;
    padding: 74px 0 77px;
    border-top: 1px solid rgba(var(--sopot-black-rgb), .10);
}

.brand-one .swiper-slide {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: relative;
}

.brand-one .swiper-slide a {
    position: relative;
    display: inline-block;
}

.brand-one .swiper-slide>a>img {
    opacity: 1;
    transition: all 0.2s ease-in-out 0.1s;
}

.brand-one .swiper-slide:hover>a>img {
    opacity: 0;
    transition: all 0.8s ease-in-out 0.1s;
}

.brand-one__overly {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    transform: translateY(55px);
    transition: all 0.4s ease-in-out 0.1s;
}

.brand-one .swiper-slide:hover .brand-one__overly {
    transform: translateY(0px);
}

.brand-one__overly a {
    position: relative;
    display: inline-block;
}

/*--------------------------------------------------------------
# Brand Two
--------------------------------------------------------------*/
.brand-two {
    border-top: none;
    border-bottom: 1px solid rgba(var(--sopot-black-rgb), .10);
}

/*--------------------------------------------------------------
# Skill One
--------------------------------------------------------------*/
.skill-one {
    position: relative;
    display: block;
}

.skill-one__container {
    position: relative;
    display: block;
    overflow: hidden;
}

.skill-one__left {
    position: relative;
    display: block;
    float: left;
    width: 50%;
    min-height: 722px;
}

.skill-one__bg {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.skill-one__arrow-box {
    position: absolute;
    top: 0;
    right: -30px;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.skill-one__arrow-box-inner {
    position: relative;
    display: block;
    width: 50px;
    height: 140px;
}

.skill-one__arrow-box-inner:before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    border-right: 50px solid var(--sopot-base);
    border-top: 70px solid transparent;
    border-bottom: 70px solid transparent;
}


.skill-one__right {
    position: relative;
    display: block;
    float: right;
    width: 50%;
    min-height: 722px;
    z-index: 1;
}

.skill-one__bg-two {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    z-index: -1;
}

.skill-one__bg-two:before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    content: "";
    background-color: rgba(37, 38, 40, .96);
}

.skill-one__content {
    position: relative;
    display: block;
    max-width: 470px;
    width: 100%;
    margin-left: 100px;
    padding-top: 120px;
    padding-bottom: 120px;
}

.skill-one__content .section-title {
    margin-bottom: 18px;
}

.skill-one__content .section-title__title {
    color: var(--sopot-white);
}

.skill-one__text {
    color: var(--sopot-white);
    padding-bottom: 17px;
}

.progress-levels {
    position: relative;
    display: block;
    margin-bottom: 50px;
}

.progress-levels .progress-box {
    position: relative;
    display: block;
    margin-bottom: 17px;
}

.progress-levels .progress-box.last-child {
    margin-bottom: 0;
}

.progress-levels .progress-box .inner {
    position: relative;
    display: block;
}

.progress-levels .progress-box .bar {
    position: relative;
    display: block;
}

.progress-levels .progress-box .bar .bar-innner {
    position: relative;
    width: 100%;
    height: 4px;
    background-color: var(--sopot-white);
    border-radius: 0px;
}

.progress-levels .progress-box .bar .bar-fill {
    position: absolute;
    top: 0%;
    left: 0px;
    bottom: 0%;
    width: 0px;
    height: 4px;
    border-radius: 0px;
    background: var(--sopot-base);
    transition: all 2000ms ease 300ms;
}

.progress-levels .progress-box .bar .bar-innner .skill-percent {
    position: absolute;
    top: -32px;
    right: 0;
    width: 40px;
    height: 25px;
    display: block;
    text-align: center;
    padding: 0;
    z-index: 1;
    color: var(--sopot-white);
}

.progress-levels .progress-box .inner .count-text {
    position: relative;
    color: var(--sopot-white);
    font-size: 16px;
    line-height: 20px;
    font-weight: 400;
    display: inline-block;
    float: none;
}

.progress-levels .progress-box .inner .percent {
    position: relative;
    color: var(--sopot-white);
    font-size: 16px;
    line-height: 20px;
    font-weight: 400;
    display: inline-block;
    float: none;
    margin-left: -2px;
}

.progress-levels .progress-box .inner .text {
    position: relative;
    color: var(--sopot-white);
    font-size: 17px;
    max-width: 370px;
    font-weight: 400;
    letter-spacing: 0;
    text-transform: capitalize;
    padding-bottom: 7px;
}

.skill-one__btn:hover {
    color: var(--sopot-white);
}

.skill-one__btn:after,
.skill-one__btn:before {
    background-color: var(--sopot-pink);
}

/*--------------------------------------------------------------
# Team One
--------------------------------------------------------------*/
.team-one {
    position: relative;
    display: block;
    padding: 120px 0 90px;
}

.team-one__single {
    position: relative;
    display: block;
    margin-bottom: 30px;
}

.team-one__img {
    position: relative;
    display: block;
    overflow: hidden;
    margin-right: 25px;
    margin-left: 25px;
}

.team-one__img:before {
    position: absolute;
    content: '';
    left: 0px;
    bottom: 0px;
    width: 50%;
    height: 50%;
    background-color: rgba(var(--sopot-base-rgb), .70);
    visibility: hidden;
    opacity: 0;
    transform-style: preserve-3d;
    transform: perspective(370px) translateY(100%);
    transform-origin: left;
    transition: all 900ms ease;
    -moz-transition: all 900ms ease;
    -webkit-transition: all 900ms ease;
    -ms-transition: all 900ms ease;
    -o-transition: all 900ms ease;
    z-index: 1;
}

.team-one__single:hover .team-one__img:before {
    visibility: visible;
    opacity: 1;
    transform: perspective(370px) translateY(0%);
}

.team-one__img:after {
    position: absolute;
    content: '';
    top: 0px;
    right: 0px;
    width: 50%;
    height: 50%;
    background-color: rgba(var(--sopot-base-rgb), .70);
    visibility: hidden;
    opacity: 0;
    transform-style: preserve-3d;
    transform: perspective(370px) translateY(-100%);
    transform-origin: top;
    transition: all 900ms ease;
    -moz-transition: all 900ms ease;
    -webkit-transition: all 900ms ease;
    -ms-transition: all 900ms ease;
    -o-transition: all 900ms ease;
    z-index: 1;
}

.team-one__single:hover .team-one__img:after {
    visibility: visible;
    opacity: 1;
    transform: perspective(370px) translateY(0%);
}

.team-one__img img {
    width: 100%;
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transition: -webkit-transform .4s ease;
    transition: -webkit-transform .4s ease;
    transition: transform .4s ease;
    transition: transform .4s ease, -webkit-transform .4s ease;
}

.team-one__single:hover .team-one__img img {
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
}

.team-one__content {
    position: relative;
    display: block;
    text-align: center;
    padding: 28px 0 35px;
    z-index: 1;
}

.team-one__content:before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    content: "";
    background-color: var(--sopot-white);
    box-shadow: 0px 0px 48.51px 0.49px rgba(0, 0, 0, 0.2);
    border: 2px solid transparent;
    opacity: 0.65;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
    z-index: -1;
}
.team-one__content img{
    height: 60px;
}
.team-one__single:hover .team-one__content:before {
    border: 2px solid var(--sopot-base);
}

.team-one__name {
    font-size: 25px;
    line-height: 32px;
}

.team-one__name a {
    color: var(--sopot-black);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.team-one__name a:hover {
    color: var(--sopot-base);
}

.team-one__social {
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    justify-content: center;
    margin-top: 13px;
}

.team-one__social a {
    position: relative;
    height: 40px;
    width: 40px;
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    text-align: center;
    color: var(--sopot-gray);
    font-size: 15px;
    border-radius: 0%;
    overflow: hidden;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
    border: 1px solid var(--sopot-gray);
    z-index: 1;
}

.team-one__social a:hover {
    color: var(--sopot-white);
    border: 1px solid var(--sopot-base);
}

.team-one__social a:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    background-color: var(--sopot-base);
    border-radius: 0%;
    transform: scale(0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.62, 0.21, 0.45, 1.52);
    z-index: -1;
}

.team-one__social a:hover:before {
    transform: scaleX(1);
}

.team-one__social a+a {
    margin-left: 15px;
}

/*--------------------------------------------------------------
# Video One
--------------------------------------------------------------*/
.video-one {
    position: relative;
    display: block;
    z-index: 1;
}

.video-one__content-box {
    position: relative;
    display: block;
    padding: 135px 0 225px;
    overflow: hidden;
    z-index: 1;
}

.video-one-shape {
    position: absolute;
    top: -250px;
    left: 135px;
    height: 390px;
    width: 390px;
    background-color: var(--sopot-base);
    border-radius: 50%;
    z-index: 2;
}

.video-one-shape:before {
    position: absolute;
    top: -25px;
    bottom: -25px;
    left: -25px;
    right: -25px;
    border: 1px dashed var(--sopot-white);
    content: "";
    border-radius: 50%;
}

.video-one-bg {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    z-index: 1;
}

.video-one-bg:before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    content: "";
    background-color: rgba(37, 38, 40, .90);
    z-index: -1;
}

.video-one__inner {
    position: relative;
    display: block;
    text-align: center;
    z-index: 2;
}

.video-one__video-link {
    position: relative;
    display: block;
    z-index: 2;
}

.video-one__video-icon {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    font-size: 25px;
    color: var(--sopot-base);
    background-color: var(--sopot-white);
    border-radius: 50%;
    margin: 0 auto;
    transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -webkit-transition: all 0.5s ease;
    -ms-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
}

.video-one__video-icon:hover {
    background-color: var(--sopot-base);
    color: var(--sopot-white);
}

.video-one__video-icon:before {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    content: "";
    border-radius: 50%;
    z-index: -1;
    background-color: var(--sopot-white);
    opacity: 0.52;
}

.video-one__video-icon:after {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    content: "";
    border-radius: 50%;
    z-index: -1;
    background-color: var(--sopot-white);
    opacity: 0.43;
}

.video-one__video-link .ripple,
.video-one__video-icon .ripple:before,
.video-one__video-icon .ripple:after {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 90px;
    height: 90px;
    transform: translate(-50%, -50%);
    -ms-box-shadow: 0 0 0 0 rgba(var(--sopot-base-rgb), 0.6);
    -o-box-shadow: 0 0 0 0 rgba(var(--sopot-base-rgb), 0.6);
    box-shadow: 0 0 0 0 rgba(var(--sopot-base-rgb), 0.6);
    -webkit-animation: ripple 3s infinite;
    animation: ripple 3s infinite;
    border-radius: 50%;
}

.video-one__video-icon .ripple:before {
    -webkit-animation-delay: 0.9s;
    animation-delay: 0.9s;
    content: "";
    position: absolute;
}

.video-one__video-icon .ripple:after {
    -webkit-animation-delay: 0.6s;
    animation-delay: 0.6s;
    content: "";
    position: absolute;
}

.video-one__title {
    font-size: 35px;
    color: var(--sopot-white);
    line-height: 44px;
    margin-top: 39px;
    margin-bottom: 16px;
}

.video-one__text {
    color: var(--sopot-white);
    font-size: 16px;
    line-height: 25px;
}

.video-one__btn {
    margin-top: 48px;
}

.video-one__btn:hover {
    color: var(--sopot-black);
}

.video-one__btn:after,
.video-one__btn:before {
    background-color: var(--sopot-white);
}

/*--------------------------------------------------------------
# Testimonial One
--------------------------------------------------------------*/
.testimonial-one {
    position: relative;
    display: block;
    padding: 0 0 120px;
    margin-top: -105px;
}

.testimonial-one__carousel {
    position: relative;
    display: block;
}

.testimonial-one__single {
    position: relative;
    display: block;
    border-radius: 5px;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 0px 13px 0px rgba(0, 0, 0, 0.07);
    text-align: center;
    padding: 50px 45px 43px;
    z-index: 1;
}

.testimonial-one__single:before {
    position: absolute;
    bottom: 0px;
    right: 0;
    left: 0;
    content: "";
    background-color: var(--sopot-base);
    height: 5px;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    transform: scaleX(0.0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.62, 0.25, 0.45, 0.22);
    z-index: -1;
}

.testimonial-one__single:hover:before {
    transform: scaleX(1.0);
}

.testimonial-one__img {
    position: relative;
    display: inline-block;
    border-radius: 50%;
}

.testimonial-one__img img {
    width: 90px !important;
    border-radius: 50%;
}

.testimonial-one__quote {
    height: 35px;
    width: 35px;
    background-color: var(--sopot-base);
    border-radius: 50%;
    color: var(--sopot-white);
    font-size: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 50%;
    right: -15px;
    transform: translateY(-50%);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.testimonial-one__single:hover .testimonial-one__quote {
    background-color: var(--sopot-black);
}

.testimonial-one__text {
    font-size: 16px;
    font-style: italic;
    padding-top: 19px;
    padding-bottom: 35px;
}

.testimonial-one__client-info {
    position: relative;
    display: block;
}

.testimonial-one__client-info:before {
    position: absolute;
    top: -12px;
    left: 50%;
    height: 1px;
    width: 25px;
    content: "";
    background-color: var(--sopot-base);
    transform: translateX(-50%);
}

.testimonial-one__client-name {
    font-size: 25px;
    line-height: 35px;
}

.testimonial-one__carousel.owl-carousel .owl-stage-outer {
    overflow: visible;
}

.testimonial-one__carousel.owl-carousel .owl-item {
    opacity: 0;
    visibility: hidden;
    transition: opacity 500ms ease, visibility 500ms ease;
}

.testimonial-one__carousel.owl-carousel .owl-item.active {
    opacity: 1;
    visibility: visible;
}

/*--------------------------------------------------------------
# Blog One
--------------------------------------------------------------*/
.blog-one {
    position: relative;
    display: block;
    padding: 120px 0px 90px;
    overflow: hidden;
    z-index: 1;
}

.blog-one::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 541px;
    background-color: rgba(var(--sopot-base-rgb), .10);
    content: "";
    z-index: -1;
}

.blog-one__single {
    position: relative;
    display: block;
    margin-bottom: 30px;
}

.blog-one__img {
    position: relative;
    display: block;
    overflow: hidden;
}

.blog-one__img img {
    width: 100%;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.blog-one__single:hover .blog-one__img img {
    transform: scale(1.05);
}

.blog-one__img>a {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(var(--sopot-black-rgb), 0.3);
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 30px;
    color: var(--sopot-base);
    transition: opacity 500ms ease, visibility 500ms ease, transform 500ms ease;
    visibility: hidden;
    opacity: 0;
    transform: translateY(-30%);
}

.blog-one__img>a>span {
    position: relative;
}

.blog-one__img>a>span::before {
    content: "";
    width: 20px;
    height: 2px;
    background-color: var(--sopot-white);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: all 500ms ease;
}

.blog-one__img>a>span::after {
    content: "";
    transition: all 500ms ease;
    width: 2px;
    height: 20px;
    background-color: var(--sopot-white);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.blog-one__img>a:hover>span::before,
.blog-one__img>a:hover>span::after {
    background-color: var(--sopot-base);
}

.blog-one__single:hover .blog-one__img>a {
    visibility: visible;
    transform: translateY(0%);
    opacity: 1;
}


.blog-one__content {
    position: relative;
    display: block;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 5px 13px 0px rgba(0, 0, 0, 0.12);
    padding: 35px 30px 38px;
}

.blog-one__content:before {
    position: absolute;
    bottom: 0px;
    left: 0px;
    right: 0px;
    content: "";
    height: 3px;
    background-color: var(--sopot-base);
    transition: transform 500ms ease;
    transform: scale(0, 1);
    transform-origin: left center;
}

.blog-one__single:hover .blog-one__content:before {
    transform: scale(1, 1);
    transform-origin: right center;
}

.blog-one__meta {
    position: relative;
    display: flex;
    align-items: center;
}

.blog-one__cat {
    position: relative;
    display: inline-block;
    padding: 7px 10px 4px;
    z-index: 1;
}

.blog-one__cat:before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    content: "";
    background-color: rgba(var(--sopot-base-rgb), .20);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
    z-index: -1;
}

.blog-one__single:hover .blog-one__cat:before {
    background-color: rgba(var(--sopot-base-rgb), 1);
}

.blog-one__cat p {
    font-size: 14px;
    color: var(--sopot-base);
    line-height: 14px;
    text-transform: uppercase;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.blog-one__single:hover .blog-one__cat p {
    color: var(--sopot-white);
}

.blgo-one__date {
    position: relative;
    display: flex;
    align-items: center;
    margin-left: 20px;
}

.blgo-one__date p {
    font-size: 14px;
    color: var(--sopot-base);
    line-height: 14px;
}

.blgo-one__date span {
    padding-right: 5px;
}

.blog-one__title {
    font-size: 24px;
    line-height: 34px;
    font-weight: 600;
    margin-top: 24px;
    margin-bottom: 9px;
}

.blog-one__title a {
    color: var(--sopot-black);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.blog-one__single:hover .blog-one__title a {
    color: var(--sopot-base);
}

/*--------------------------------------------------------------
# Site Footer
--------------------------------------------------------------*/
.site-footer {
    position: relative;
    display: block;
}

.site-footer__top {
    position: relative;
    display: block;
    padding-top: 120px;
    padding-bottom: 120px;
    background-color: var(--sopot-black);
}

.footer-widget__about {
    position: relative;
    display: block;
    margin-right: 108px;
}

.footer-widget__about-logo {
    position: relative;
    display: block;
}

.footer-widget__list {
    position: relative;
    display: block;
    margin-top: 33px;
    margin-bottom: 44px;
}

.footer-widget__list li {
    position: relative;
    display: flex;
}

.footer-widget__list li+li {
    margin-top: 16px;
}

.footer-widget__list li .icon {
    position: relative;
    display: block;
    top: 3px;
}

.footer-widget__list li .icon span {
    font-size: 16px;
    color: var(--sopot-base);
}

.footer-widget__list li .text {
    margin-left: 10px;
}

.footer-widget__list li .text p {
    font-size: 17px;
    color: var(--sopot-white);
}

.footer-widget__list li .text p a {
    color: var(--sopot-white)
}

.footer-widget__social {
    position: relative;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
}

.footer-widget__social a {
    position: relative;
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    text-align: center;
    color: var(--sopot-white);
    background-color: rgba(var(--sopot-white-rgb), .06);
    border-radius: 50%;
    font-size: 15px;
    height: 40px;
    width: 40px;
    transform: rotate(0);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
    z-index: 1;
}

.footer-widget__social a+a {
    margin-left: 10px;
}

.footer-widget__social a:hover {
    color: var(--sopot-white);
    transform: rotate(360deg);
    background-color: var(--sopot-base);
}

.footer-widget__social a:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    background-color: var(--sopot-base);
    border-radius: 50%;
    transform: scale(0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.62, 0.21, 0.45, 1.52);
    z-index: -1;
}

.footer-widget__social a:hover:before {
    transform: scaleX(1);
}

.footer-widget__title {
    position: relative;
    display: block;
    font-size: 25px;
    color: var(--sopot-white);
    line-height: 30px;
    margin-bottom: 31px;
    margin-top: -5px;
}

.footer-widget__service {
    position: relative;
    display: block;
}

.footer-widget__service-list {
    position: relative;
    display: block;
}

.footer-widget__service-list li {
    position: relative;
    display: block;
}

.footer-widget__service-list li+li {
    margin-top: 16px;
}

.footer-widget__service-list li a {
    position: relative;
    display: inline-block;
    padding-left: 15px;
    font-size: 17px;
    color: var(--sopot-white);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.footer-widget__service-list li a:hover {
    color: var(--sopot-base);
}

.footer-widget__service-list li a:before {
    position: absolute;
    top: 52%;
    left: 0;
    content: "";
    height: 5px;
    width: 5px;
    border-radius: 50%;
    background-color: var(--sopot-white);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
    transform: translateY(-50%) scale(1);
}

.footer-widget__service-list li a:hover:before {
    transform: translateY(-50%) scale(1.5);
    background-color: var(--sopot-base);
}

.footer-widget__links {
    position: relative;
    display: block;
}

.footer-widget__links-list {
    position: relative;
    display: block;
}

.footer-widget__links-list li {
    position: relative;
    display: block;
}

.footer-widget__links-list li a {
    position: relative;
    display: inline-block;
    padding-left: 15px;
    font-size: 17px;
    color: var(--sopot-white);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.footer-widget__links-list li+li {
    margin-top: 16px;
}

.footer-widget__links-list li a:hover {
    color: var(--sopot-base);
}

.footer-widget__links-list li a:before {
    position: absolute;
    top: 52%;
    left: 0;
    content: "";
    height: 5px;
    width: 5px;
    border-radius: 50%;
    background-color: var(--sopot-white);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
    transform: translateY(-50%) scale(1);
}

.footer-widget__links-list li a:hover:before {
    transform: translateY(-50%) scale(1.5);
    background-color: var(--sopot-base);
}

.footer-widget__blog {
    position: relative;
    display: block;
}

.footer-widget__blog-list {
    position: relative;
    display: block;
}

.footer-widget__blog-list li {
    position: relative;
    display: block;
    padding-left: 70px;
    overflow: hidden;
}

.footer-widget__blog-list li+li {
    margin-top: 30px;
}

.footer-widget__blog-img {
    position: absolute;
    top: 0;
    left: 0;
    width: 70px;
}

.footer-widget__blog-img:before {
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    content: "";
    background-color: rgba(var(--sopot-base-rgb), .82);
    transition-delay: .1s;
    transition-timing-function: ease-in-out;
    transition-duration: .5s;
    transition-property: all;
    transform-origin: bottom;
    transform-style: preserve-3d;
    transform: scaleY(0);
    z-index: 1;
}

.footer-widget__blog-list li:hover .footer-widget__blog-img:before {
    transform: scaleY(1.0);
}

.footer-widget__blog-img img {
    width: 100%;
}

.footer-widget__blog-img>a {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    color: var(--sopot-white);
    transform: translateY(-100%);
    transition-delay: .1s;
    transition-timing-function: ease-in-out;
    transition-duration: .5s;
    transition-property: all;
    visibility: hidden;
    opacity: 0;
    z-index: 2;
}

.footer-widget__blog-list li:hover .footer-widget__blog-img>a {
    transform: translateY(0%);
    visibility: visible;
    opacity: 1;
}

.footer-widget__blog-content {
    position: relative;
    display: block;
    margin-left: 30px;
}

.footer-widget__blog-text {
    font-size: 14px;
    margin: 0;
    line-height: 23px;
}

.footer-widget__blog-text a {
    color: rgba(var(--sopot-white-rgb), .50);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.footer-widget__blog-list li:hover .footer-widget__blog-text a {
    color: var(--sopot-base);
}

.footer-widget__blog-date {
    font-size: 14px;
    margin: 0;
    line-height: 23px;
}

.footer-widget__map {
    position: relative;
    display: block;
}

.footer-widget__map-box {
    position: relative;
    display: block;
    padding-top: 7px;
}

.footer-widget-map__one {
    position: relative;
    display: block;
    width: 100%;
    border: none;
    height: 185px;
}

.site-footer__bottom {
    position: relative;
    display: block;
    text-align: center;
    padding: 32px 0;
    z-index: 1;
}

.site-footer__bottom:before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    content: "";
    background-color: #0a262c;
    z-index: -1;
}

.site-footer__bottom-text {
    font-size: 16px;
    margin: 0;
    color: rgba(var(--sopot-white-rgb), .5);
}

.site-footer__bottom-text a {
    font-weight: 600;
    color: rgba(var(--sopot-white-rgb), .50);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.site-footer__bottom-text a:hover {
    color: var(--sopot-base);
}

/*--------------------------------------------------------------
# Feature Two
--------------------------------------------------------------*/
.feature-two {
    position: relative;
    display: block;
    padding: 120px 0 90px;
}

.feature-two::after {
    position: absolute;
    bottom: 0;
    right: -30px;
    content: "";
    width: 0;
    border-left: 140px solid transparent;
    border-bottom: 160px solid var(--sopot-base);
    -webkit-animation-name: float-bob-x;
    animation-name: float-bob-x;
    -webkit-animation-duration: 2s;
    animation-duration: 2s;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
}

.feature-two__single {
    position: relative;
    display: block;
    margin-bottom: 30px;
}

.feature-two__img {
    position: relative;
    display: block;
    overflow: hidden;
}

.feature-two__img:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    background-color: rgba(var(--sopot-black-rgb), .30);
    opacity: 1;
    -webkit-transition: .5s;
    -o-transition: .5s;
    transition: .5s;
    -webkit-transform: perspective(400px) rotateX(-90deg) scale(0.2);
    -ms-transform: perspective(400px) rotateX(-90deg) scale(0.2);
    transform: perspective(400px) rotateX(-90deg) scale(0.2);
    -webkit-transform-origin: top;
    -ms-transform-origin: top;
    transform-origin: top;
    z-index: 1;
}

.feature-two__single:hover .feature-two__img:before {
    opacity: 1;
    -webkit-transform: perspective(400px) rotateX(0deg) scale(1.0);
    -ms-transform: perspective(400px) rotateX(0deg) scale(1.0);
    transform: perspective(400px) rotateX(0deg) scale(1.0);
}

.feature-two__img img {
    width: 100%;
    transition-delay: .1s;
    transition-timing-function: ease-in-out;
    transition-duration: .7s;
    transition-property: all;
}

.feature-two__single:hover .feature-two__img img {
    transform: scale(1.1) rotate(2deg);
}

.feature-two__content {
    position: relative;
    display: block;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 0px 35px 0px rgba(0, 0, 0, 0.06);
    padding: 30px 30px 23px;
    z-index: 1;
}

.feature-two__content:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    background-color: var(--sopot-base);
    transition-delay: .1s;
    transition-timing-function: ease-in-out;
    transition-duration: .5s;
    transition-property: all;
    opacity: 1;
    transform-origin: bottom;
    transform-style: preserve-3d;
    transform: scaleY(0);
    z-index: -1;
}

.feature-two__single:hover .feature-two__content:before {
    transform: scaleY(1.0);
}

.feature-two__top {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 18px;
}

.feature-two__icon {
    position: relative;
    display: flex;
    align-items: center;
}

.feature-two__icon span {
    font-size: 45px;
    color: var(--sopot-base);
    position: relative;
    display: inline-block;
    transition: all 500ms linear;
    transition-delay: 0.1s;
    transform: scale(1);
}

.feature-two__single:hover .feature-two__icon span {
    color: var(--sopot-white);
    transform: scale(1.2);
}

.feature-two__icon:before {
    position: absolute;
    top: 0;
    right: -5px;
    height: 35px;
    width: 35px;
    background-color: rgba(var(--sopot-black-rgb), .15);
    content: "";
    border-radius: 50%;
    transition: all 500ms linear;
    transition-delay: 0.1s;
    transform: scale(1);
}

.feature-two__single:hover .feature-two__icon:before {
    transform: scale(.9);
}

.feature-two__title {
    font-size: 25px;
    line-height: 25px;
    margin-left: 20px;
    position: relative;
    display: inline-block;
}

.feature-two__title:before {
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    content: "";
    background-color: var(--sopot-white);
    height: 1px;
    transform: scaleX(0.0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.62, 0.25, 0.45, 0.22);
    transition-delay: 0.3s;
}

.feature-two__single:hover .feature-two__title:before {
    transform: scaleX(1.0);
}

.feature-two__title a {
    color: var(--sopot-black);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.feature-two__single:hover .feature-two__title a {
    color: var(--sopot-white);
}

.feature-two__text {
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.feature-two__single:hover .feature-two__text {
    color: var(--sopot-white);
}

/*--------------------------------------------------------------
# About Two
--------------------------------------------------------------*/
.about-two {
    position: relative;
    display: block;
    padding: 120px 0 120px;
    background-color: #f8f8f9;
    z-index: 1;
}

.about-two-shape-1 {
    position: absolute;
    bottom: 0;
    right: 110px;
}

.about-two-shape-1 img {
    width: auto;
}

.about-two__left {
    position: relative;
    display: block;
    margin-right: 55px;
    z-index: 1;
}

.about-two__bg {
    position: absolute;
    top: -120px;
    bottom: -200px;
    right: 245px;
    left: -375px;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    z-index: -1;
}

.about-two__bg:before {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    content: "";
    background-color: rgba(var(--sopot-base-rgb), .95);
}

.about-two__img-box {
    position: relative;
    display: block;
}

.about-two__img {
    position: relative;
    display: block;
    overflow: hidden;
    z-index: 1;
}

.about-two__img:after {
    position: absolute;
    top: 0;
    left: -100%;
    display: block;
    content: '';
    width: 50%;
    height: 100%;
    background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, .3) 100%);
    transform: skewX(-25deg);
    z-index: 1;
}

.about-two__img-box:hover .about-two__img:after {
    -webkit-animation: shine 1.5s;
    animation: shine 1.5s;
}

.about-two__img img {
    width: 100%;
}

.about-two__small-img {
    position: absolute;
    bottom: -70px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
}

.about-two__small-img:before {
    position: absolute;
    top: -10px;
    left: -10px;
    bottom: -10px;
    right: -10px;
    content: "";
    background-color: var(--sopot-white);
    z-index: -1;
}

.about-two__small-img img {
    width: auto;
}

.about-two__right {
    position: relative;
    display: block;
    margin-right: 110px;
}

.about-two__right .section-title {
    margin-bottom: 33px;
}

.about-two__points {
    position: relative;
    display: block;
    margin-top: 40px;
    margin-bottom: 37px;
}

.about-two__points li {
    position: relative;
    display: flex;
    align-items: center;
}

.about-two__points li+li {
    margin-top: 10px;
}

.about-two__points li .icon {
    position: relative;
    display: flex;
    align-items: center;
}

.about-two__points li .icon span {
    font-size: 20px;
    color: var(--sopot-black);
    position: relative;
    display: inline-block;
    transition: all 500ms linear;
    transition-delay: 0.1s;
}

.about-two__points li:hover .icon span {
    transform: rotateY(360deg);
    color: var(--sopot-base);
}

.about-two__points li .text {
    margin-left: 10px;
}

.about-two__points li .text p {
    color: var(--sopot-black);
    font-weight: 500;
    font-family: var(--sopot-font-two);
}

/*--------------------------------------------------------------
# Recent Event
--------------------------------------------------------------*/
.recent-event {
    position: relative;
    display: block;
    padding: 120px 0 90px;
}

.recent-event__single {
    position: relative;
    display: block;
    align-items: center;
    margin-bottom: 30px;
    padding-left: 268px;
}

.recent-event__img {
    position: absolute;
    top: 0;
    left: 0;
    max-width: 268px;
    width: 100%;
    z-index: 1;
    overflow: hidden;
}

.recent-event__img:before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    content: "";
    background-color: rgba(var(--sopot-black-rgb), .50);
    transition: all 700ms ease;
    transform: translateY(-100%);
    z-index: 1;
}

.recent-event__single:hover .recent-event__img:before {
    transform: translateY(0%);
}

.recent-event__img img {
    width: 100%;
}

.recent-event__btn-box {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translateY(100px);
    opacity: 0;
    transition: all 700ms ease;
    z-index: 2;
}

.recent-event__single:hover .recent-event__btn-box {
    transform: translateY(0px);
    opacity: 1;
    transition-delay: 0.3s;
}

.recent-event__btn {
    padding: 12px 40px 12px;
}

.recent-event__content {
    position: relative;
    display: block;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 0px 15.84px 0.16px rgba(0, 0, 0, 0.12);
    padding: 27px 20px 25px;
}

.recent-event__meta {
    position: relative;
    display: flex;
    align-items: center;
}

.recent-event__meta li+li {
    margin-left: 20px;
}

.recent-event__meta li p {
    font-size: 14px;
}

.recent-event__meta li p span {
    font-size: 15px;
    color: var(--sopot-base);
    padding-right: 6px;
}

.recent-event__title {
    font-size: 25px;
    line-height: 35px;
    margin-top: 5px;
    margin-bottom: 3px;
}

.recent-event__title a {
    color: var(--sopot-black);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.recent-event__single:hover .recent-event__title a {
    color: var(--sopot-base);
}

/*--------------------------------------------------------------
# Counter One
--------------------------------------------------------------*/
.counter-one {
    position: relative;
    display: block;
    padding: 120px 0 120px;
    z-index: 1;
}

.counter-one-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    z-index: -1;
}

.counter-one-bg:before {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    content: "";
    background-color: rgba(37, 38, 40, .96);
    z-index: -1;

}

.counter-one .section-title__title {
    color: var(--sopot-white);
}

.counter-one__single {
    position: relative;
    display: block;
    margin-bottom: 30px;
    z-index: 1;
}

.counter-one__single:before {
    position: absolute;
    top: 0;
    left: 1px;
    right: 1px;
    content: "";
    height: 1px;
    background-color: var(--sopot-base);
    transform: scaleX(0.0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.62, 0.25, 0.45, 0.22);
    z-index: 2;
}

.counter-one__single:after {
    position: absolute;
    bottom: 0;
    left: 1px;
    right: 1px;
    content: "";
    height: 1px;
    background-color: var(--sopot-base);
    transform: scaleX(0.0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.62, 0.25, 0.45, 0.22);
    z-index: 2;
}

.counter-one__single:hover:after,
.counter-one__single:hover:before {
    transform: scaleX(1.0);
}

.counter-one__content {
    position: relative;
    display: block;
    text-align: center;
    padding-top: 20px;
    padding-bottom: 39px;
    border: 1px solid var(--sopot-white);
    transition: all 0.4s;
}

.counter-one__single:hover .counter-one__content {
    border: 1px solid transparent;
}

.counter-one__content:before {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    content: "";
    width: 1px;
    background-color: var(--sopot-base);
    transform: scaleY(0.0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.62, 0.25, 0.45, 0.22);
    z-index: 2;
}

.counter-one__content:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    content: "";
    width: 1px;
    background-color: var(--sopot-base);
    transform: scaleY(0.0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.62, 0.25, 0.45, 0.22);
    z-index: 2;
}

.counter-one__single:hover .counter-one__content:after,
.counter-one__single:hover .counter-one__content:before {
    transform: scaleY(1.0);
}

.counter-one__content h3 {
    font-size: 50px;
    color: var(--sopot-white);
    line-height: 60px !important;
    font-family: var(--sopot-font-two) !important;
    font-weight: 700;
    letter-spacing: 0.015em;
}

.counter-one__letter {
    font-size: 50px;
    color: var(--sopot-white);
    font-weight: 700;
    font-family: var(--sopot-font-two);
    line-height: 60px;
    position: relative;
    top: 12px;
}

.counter-one__text {
    color: var(--sopot-white);
}

.counter-one__btn-box {
    position: relative;
    display: block;
    margin-top: 30px;
}

.counter-one__btn:hover {
    color: var(--sopot-black);
}

.counter-one__btn:after,
.counter-one__btn:before {
    background-color: var(--sopot-white);
}

.counter-one .odometer-formatting-mark {
    display: none;
}

/*--------------------------------------------------------------
# Causes Two
--------------------------------------------------------------*/
.causes-two {
    position: relative;
    display: block;
    background-color: var(--sopot-white);
    z-index: 1;
}

.causes-two:before {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    content: "";
    background-color: rgba(var(--sopot-base-rgb), .10);
    height: 435px;
    z-index: -1;
}

.causes-two:after {
    display: none;
}

/*--------------------------------------------------------------
# FAQ One
--------------------------------------------------------------*/
.faq-one {
    position: relative;
    display: block;
    padding: 120px 0 120px;
    z-index: 1;
}

.faq-one-bg {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    z-index: -1;
}

.faq-one-bg:before {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    content: "";
    background-color: rgba(248, 248, 249, .96);
}

.faq-one__left {
    position: relative;
    display: block;
}

.faq-one__faq {
    position: relative;
    display: block;
}

.faq-one-accrodion .accrodion {
    position: relative;
    display: block;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
    background-color: var(--sopot-white);
}

.faq-one-accrodion .accrodion.active {
    background-color: var(--sopot-extra);
}

.faq-one-accrodion .accrodion-title {
    position: relative;
    display: block;
    cursor: pointer;
    padding: 22px 40px 22px;
    padding-right: 27px;
    transition: all 200ms linear;
    transition-delay: 0.1s;
    border: 1px solid #dddddd;
    border-radius: 5px;
}

.faq-one-accrodion .accrodion-title h4 {
    margin: 0;
    font-size: 17px;
    line-height: 28px;
    font-weight: 400;
    color: var(--sopot-gray);
    position: relative;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
    font-family: var(--sopot-font);
}

.faq-one-accrodion .accrodion+.accrodion {
    margin-top: 20px;
}

.faq-one-accrodion .accrodion.active .accrodion-title {
    border: 1px solid transparent;
    background-color: var(--sopot-base);
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.faq-one-accrodion .accrodion.active .accrodion-title h4 {
    color: var(--sopot-white);
}

.faq-one-accrodion .accrodion-title h4::before {
    content: "\f067";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    font-size: 13px;
    color: var(--sopot-base);
    position: absolute;
    top: 50%;
    right: 0;
    height: 35px;
    width: 35px;
    border-radius: 50%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.faq-one-accrodion .accrodion.active .accrodion-title h4::before {
    content: "\f068";
    color: var(--sopot-white);
    height: 35px;
    width: 35px;
    border-radius: 50%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
}

.faq-one-accrodion .accrodion-content {
    position: relative;
    padding: 28px 40px 28px;
    border: 1px solid #dddddd;
    border-top: 0;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
}

.faq-one-accrodion .accrodion-content p {
    font-size: 17px;
    margin: 0;
}

.faq-one__right {
    position: relative;
    display: block;
}

.faq-one__img {
    position: absolute;
    top: 69px;
    bottom: 0;
    left: 120px;
    right: 0;
}

.faq-one__img img {
    width: auto;
}


/*--------------------------------------------------------------
# video Two
--------------------------------------------------------------*/
.video-two {
    position: relative;
    display: block;
}

.video-two .video-one__content-box {
    padding: 135px 0 120px;
}

.video-two-shape {
    position: absolute;
    bottom: -270px;
    right: 100px;
    height: 390px;
    width: 390px;
    background-color: var(--sopot-base);
    border-radius: 50%;
    z-index: 2;
}

.video-two-shape:before {
    position: absolute;
    top: -25px;
    bottom: -25px;
    left: -25px;
    right: -25px;
    border: 1px dashed var(--sopot-white);
    content: "";
    border-radius: 50%;
}

/*--------------------------------------------------------------
# Blog Two
--------------------------------------------------------------*/
.blog-two {
    position: relative;
    display: block;
}

.blog-two::before {
    display: none;
}

.blog-two::after {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 397px;
    background-color: rgba(var(--sopot-base-rgb), .05);
    content: "";
    z-index: -1;
}

/*--------------------------------------------------------------
# Help The Causes
--------------------------------------------------------------*/
.help-the-causes {
    position: relative;
    display: block;
}

.help-the-causes__wrapper {
    position: relative;
    display: block;
    overflow: hidden;
}

.help-the-causes__left {
    position: relative;
    display: block;
    float: left;
    width: 60%;
    min-height: 606px;
}

.help-the-causes__left-bg {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
}

.help-the-causes__right {
    position: relative;
    display: block;
    float: right;
    width: 40%;
    z-index: 1;
}

.help-the-causes__content-bg {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    z-index: 1;
}

.help-the-causes__content-bg:before {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    content: "";
    background-color: rgba(var(--sopot-black-rgb), .95);
    z-index: -1;
}

.help-the-causes__content-box {
    position: relative;
    display: block;
    padding-top: 100px;
    padding-bottom: 100px;
    margin-left: 60px;
    max-width: 450px;
    width: 100%;
    z-index: 3;
}

.help-the-causes__content-box .section-title {
    margin-bottom: 23px;
}

.help-the-causes__content-box .section-title__title {
    color: var(--sopot-white);
}

.help-the-causes__text {
    color: var(--sopot-white);
}

.help-the-causes .progress-levels {
    margin-top: 26px;
    margin-bottom: 40px;
}

/*--------------------------------------------------------------
# Two Section
--------------------------------------------------------------*/
.two-section {
    position: relative;
    display: block;
}

.two-section__wrapper {
    position: relative;
    display: block;
    overflow: hidden;
}

.two-section__left {
    position: relative;
    display: block;
    float: left;
    width: 50%;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    z-index: 1;
}

.two-section__left:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    background-color: rgba(var(--sopot-base-rgb), .90);
    z-index: -1;
}

.two-section__content-box {
    position: relative;
    display: block;
    max-width: 500px;
    margin: 0 auto;
    text-align: center;
    padding: 92px 0 100px;
}

.two-section__sub-title {
    font-size: 20px;
    color: var(--sopot-white);
    line-height: 30px;
}

.two-section__title {
    color: var(--sopot-white);
    font-size: 40px;
    line-height: 50px;
    font-weight: 600;
    text-transform: capitalize;
    margin: 22px 0 28px;
}

.two-section__btn-1 {
    background-color: var(--sopot-black);
}

.two-section__btn-1:hover {
    color: var(--sopot-black);
}

.two-section__btn-1:after,
.two-section__btn-1:before {
    background-color: var(--sopot-white);
}

.two-section__btn-2:hover {
    color: var(--sopot-black);
}

.two-section__btn-2:after,
.two-section__btn-2:before {
    background-color: var(--sopot-white);
}

.two-section__right {
    position: relative;
    display: block;
    float: right;
    width: 50%;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    z-index: 1;
}

.two-section__right:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    background-color: rgba(var(--sopot-black-rgb), .90);
    z-index: -1;
}

/*--------------------------------------------------------------
# Help People
--------------------------------------------------------------*/
.help-people {
    position: relative;
    display: block;
    padding: 120px 0 120px;
    z-index: 1;
}

.help-people-bg {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    z-index: 1;
}

.help-people-bg::before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    content: "";
    background-color: rgba(var(--sopot-black-rgb), .80);
    z-index: -1;
}

.help-people__inner {
    position: relative;
    display: block;
    max-width: 700px;
    width: 100%;
    margin: 0 auto;
    padding: 60px 60px 60px;
    text-align: center;
    background-color: var(--sopot-white);
    z-index: 5;
}

.help-people__sub-title-box {
    position: relative;
    display: inline-block;
    padding: 7px 10px 4px;
    z-index: 1;
}

.help-people__sub-title-box:before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    content: "";
    background-color: rgba(var(--sopot-base-rgb), .20);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
    z-index: -1;
}

.help-people__sub-title-box p {
    font-size: 14px;
    color: var(--sopot-base);
    line-height: 14px;
    text-transform: uppercase;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.help-people__title {
    font-size: 35px;
    line-height: 45px;
    margin-top: 10px;
    margin-bottom: 19px;
}

.help-people__btn {
    margin-top: 33px;
}

/*--------------------------------------------------------------
# Up Comming Events
--------------------------------------------------------------*/
.up-comming-events {
    position: relative;
    display: block;
    margin-top: -80px;
    z-index: 10;
}

.up-comming-events__inner {
    position: relative;
    display: block;
    background-color: rgba(255, 255, 255, 1.0);
    padding: 50px 40px;
    box-shadow: 0px 0px 35px 0px rgb(0 0 0 / 6%);
    z-index: 1;
}




.up-comming-events__left {
    position: relative;
    display: block;
}

.up-comming-events__content {
    position: relative;
    display: block;
    padding: 14px 0;
}

.up-comming-events__title {
    font-size: 26px;
    line-height: 40px;
    font-weight: 600;
    margin-bottom: 6px;
}

.up-comming-events__details {
    position: relative;
    display: flex;
    align-items: center;
}

.up-comming-events__details li+li {
    margin-left: 15px;
}

.up-comming-events__details li p {
    font-size: 14px;
    text-transform: uppercase;
}

.up-comming-events__details li p span {
    color: var(--sopot-base);
    padding-right: 5px;
}

.up-comming-events__right {
    position: relative;
    display: block;
    float: right;
}

.timer-box {
    position: relative;
    display: block;
}

.countdown-timer .default-coundown .box {
    position: relative;
    display: block;
}

.countdown-timer li {
    position: relative;
    display: inline-block;
    float: none;
    padding: 0;
    text-align: center;
    margin: 0px 5px 0px;
}

.countdown-timer li:first-child {
    margin-left: 0;
}

.countdown-timer li:last-child {
    margin-right: 0;
}

.countdown-timer li span.days,
.countdown-timer li span.hours,
.countdown-timer li span.minutes,
.countdown-timer li span.seconds {
    position: relative;
    display: block;
    width: 100px;
    height: 100px;
    color: var(--sopot-white);
    font-size: 45px;
    line-height: 70px;
    text-align: center;
    border-radius: 5px;
    background-color: var(--sopot-base);
    font-weight: 600;
}

.countdown-timer li span.timeRef {
    position: absolute;
    bottom: 14px;
    left: 50%;
    color: var(--sopot-white);
    font-size: 25px;
    line-height: 25px;
    font-weight: 500;
    text-transform: capitalize;
    display: block;
    text-align: center;
    transform: translateX(-50%);
}

/*--------------------------------------------------------------
# Support Us
--------------------------------------------------------------*/
.support-us {
    position: relative;
    display: block;
    padding: 120px 0 120px;
    z-index: 1;
}

.support-us-bg {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    z-index: -1;
}

.support-us__left {
    position: relative;
    display: block;
    z-index: 1;
}

.support-us__content-box {
    position: relative;
    display: block;
    text-align: center;
    padding: 53px 60px 60px;
    z-index: 1;
}

.support-us__content-box:before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    content: "";
    background-color: rgba(var(--sopot-white-rgb), .95);
    z-index: -1;
}

.support-us__title {
    font-size: 30px;
    line-height: 40px;
}

.support-us__text {
    padding-top: 11px;
    padding-bottom: 28px;
}

#support-us__donate-form {
    position: relative;
    display: block;
}

#support-us__donate-form .custon-box {
    position: relative;
    display: block;
}

#support-us__donate-form .custon-box input[type="text"] {
    position: relative;
    display: block;
    width: 100%;
    height: 85px;
    background-color: var(--sopot-white);
    color: var(--sopot-black);
    font-size: 18px;
    font-weight: 400;
    border-radius: 0;
    padding-left: 60px;
    padding-right: 30px;
    transition: all 200ms linear;
    transition-delay: 0.1s;
    border: 1px solid rgba(var(--sopot-black-rgb), .10);
    outline: none;
}

#support-us__donate-form .custon-box .icon {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 50px;
    color: var(--sopot-white);
    font-size: 24px;
    line-height: 87px;
    text-align: center;
    background-color: var(--sopot-black);
}

.support-us-button-box {
    position: relative;
    display: block;
}

.support-us__btn {
    border: none;
    width: 100%;
}

.support-us__donate-form-select-box {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    margin-top: 25px;
    margin-bottom: 40px;
}

.support-us__donate-form-select-box li {
    position: relative;
    display: flex;
    align-items: center;
}

.support-us__donate-form-select-box li+li {
    margin-left: 5px;
}

.support-us__donate-form-select-box li input[type=radio] {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
}

.support-us__donate-form-select-box li label {
    position: relative;
    display: block;
    background-color: var(--sopot-base);
    color: var(--sopot-white);
    font-size: 17px;
    font-weight: 400;
    text-align: center;
    margin-bottom: 0;
    cursor: pointer;
    transition: all 200ms linear;
    transition-delay: 0.1s;
    border: none;
    width: 108px;
    height: 40px;
    line-height: 40px;
}

.support-us__donate-form-select-box li :checked+label {
    color: var(--sopot-white);
    background-color: var(--sopot-black);
}

.support-us__donate-form-select-box li input[type="text"] {
    position: relative;
    display: block;
    width: 170px;
    height: 55px;
    background-color: var(--sopot-white);
    color: var(--sopot-black);
    font-size: 18px;
    line-height: 55px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 0;
    border-radius: 30px;
    transition: all 200ms linear;
    transition-delay: 0.1s;
}

.support-us__right-content {
    position: relative;
    display: block;
}

.support-us__right-content {
    position: relative;
    display: block;
    text-align: right;
}

.support-us__right-content-title {
    font-size: 65px;
    color: var(--sopot-white);
    line-height: 96px;
    letter-spacing: 0.015em;
    margin-bottom: 15px;
}

.support-us__right-content-text {
    font-size: 20px;
    color: var(--sopot-white);
    line-height: 30px;
    letter-spacing: 0.1em;
}


/*--------------------------------------------------------------
# Site Footer One
--------------------------------------------------------------*/
.site-footer-one {
    position: relative;
    display: block;
}

.site-footer-one__top {
    position: relative;
    display: block;
    background-color: var(--sopot-black);
    padding: 115px 0 106px;
}

.footer-widget-one__about {
    position: relative;
    display: block;
    margin-top: 5px;
}

.footer-widget-one__title {
    position: relative;
    display: block;
    font-size: 22px;
    color: var(--sopot-white);
    line-height: 30px;
    margin-bottom: 42px;
    font-weight: 500;
    text-transform: capitalize;
}

.footer-widget-one__about-logo {
    position: relative;
    display: block;
}

.footer-widget-one__about-logo a {
    position: relative;
    display: inline-block;
}

.footer-widget-one__about-text {
    color: rgba(var(--sopot-white-rgb), .5);
    font-size: 16px;
    line-height: 26px;
    padding-top: 29px;
    padding-bottom: 18px;
}

.footer-widget-one__list {
    position: relative;
    display: block;
}

.footer-widget-one__list li {
    position: relative;
    display: flex;
    align-items: center;
}

.footer-widget-one__list li+li {
    margin-top: 20px;
}

.footer-widget-one__list li .icon {
    position: relative;
    display: block;
    height: 35px;
    width: 35px;
    border-radius: 50%;
    background-color: var(--sopot-base);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    color: var(--sopot-white);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.footer-widget-one__list li:hover .icon {
    background-color: var(--sopot-white);
    color: var(--sopot-base);
}

.footer-widget-one__list li .text {
    margin-left: 15px;
}

.footer-widget-one__list li .text p {
    font-size: 15px;
    color: rgba(var(--sopot-white-rgb), .5);
}

.footer-widget-one__list li .text p a {
    color: rgba(var(--sopot-white-rgb), .5);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.footer-widget-one__list li:hover .text p a {
    color: var(--sopot-white);
}

.footer-widget-one__gallery {
    position: relative;
    display: block;
}

.footer-widget-one__gallery-list {
    position: relative;
    display: block;
    margin-right: -5px;
    margin-left: -5px;
}

.footer-widget-one__gallery-list li {
    position: relative;
    display: inline-block;
    padding-left: 5px;
    padding-right: 5px;
    margin-bottom: 15px;
}

.footer-widget-one__gallery-img {
    position: relative;
    display: block;
    overflow: hidden;
    z-index: 1;
}

.footer-widget-one__gallery-img:before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    content: "";
    background-color: rgba(var(--sopot-black-rgb), .90);
    transition: all 700ms ease;
    transform: translateY(-100%);
    z-index: 1;
}

.footer-widget-one__gallery-list li:hover .footer-widget-one__gallery-img:before {
    transform: translateY(0%);
}

.footer-widget-one__gallery-img img {
    width: 100%;
    transition-delay: .1s;
    transition-timing-function: ease-in-out;
    transition-duration: .7s;
    transition-property: all;
    filter: grayscale(.7) opacity(1.0);
}

.footer-widget-one__gallery-list li:hover .footer-widget-one__gallery-img img {
    transform: scale(1.1) rotate(2deg);
}

.footer-widget-one__gallery-img a {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: var(--sopot-white);
    transform: translateY(100px);
    opacity: 0;
    transition: all 700ms ease;
    z-index: 2;
}

.footer-widget-one__gallery-list li:hover .footer-widget-one__gallery-img a {
    transform: translateY(0px);
    opacity: 1;
    transition-delay: 0.3s;
}

.footer-widget-one__gallery-list li .footer-widget-one__gallery-img a:hover {
    color: var(--sopot-base);
}

.footer-widget-one__latest-works {
    position: relative;
    display: block;
}

.footer-widget-one__latest-works-list {
    position: relative;
    display: block;
}

.footer-widget-one__latest-works-list li {
    position: relative;
    display: block;
    padding: 22px 30px 23px;
    z-index: 1;
}

.footer-widget-one__latest-works-list li:before {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    content: "";
    background-color: #0a262c;
    z-index: -1;
}

.footer-widget-one__latest-works-list li+li {
    margin-top: 15px;
}

.footer-widget-one__latest-works-content {
    position: relative;
    display: block;
}

.footer-widget-one__latest-works-title {
    font-size: 16px;
    color: var(--sopot-white);
    line-height: 26px;
    font-weight: 400;
    margin-bottom: 5px;
}

.footer-widget-one__latest-works-title a {
    color: rgba(var(--sopot-white-rgb), .5);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.footer-widget-one__latest-works-title a:hover {
    color: var(--sopot-base);
}

.footer-widget-one__latest-works-date {
    color: var(--sopot-base);
    font-size: 15px;
}


.footer-widget-one__twitter-feed {
    position: relative;
    display: block;
}

.footer-widget-one__twitter-feed-content {
    position: relative;
    display: block;
}

.footer-widget-one__twitter-feed-single {
    position: relative;
    display: block;
    margin-top: -7px;
}

.footer-widget-one__twitter-feed-text {
    color: rgba(var(--sopot-white-rgb), .5);
    font-size: 16px;
    line-height: 26px;
}

.footer-widget-one__twitter-feed-text a {
    color: var(--sopot-base);
}

.footer-widget-one__twitter-feed-bottom {
    position: relative;
    display: flex;
    align-items: center;
    margin-top: 37px;
}

.footer-widget-one__twitter-feed-icon {
    position: relative;
    display: flex;
    align-items: center;
}

.footer-widget-one__twitter-feed-icon span {
    font-size: 50px;
    color: var(--sopot-base);
}

.footer-widget-one__twitter-feed-details {
    margin-left: 20px;
}

.footer-widget-one__twitter-feed-name {
    font-size: 16px;
    color: var(--sopot-white);
    line-height: 30px;
}

.footer-widget-one__twitter-feed-name span {
    font-size: 15px;
    color: var(--sopot-base);
    font-weight: 400;
    padding-left: 5px;
}

.footer-widget-one__twitter-feed-time {
    font-size: 15px;
    color: var(--sopot-base);
}

.footer-widget-one__twitter-feed-content.owl-carousel .owl-dots {
    position: absolute;
    bottom: -55px;
    left: 0px;
    right: 0;
    text-align: left;
    margin: 0;
    line-height: 0;
}

.footer-widget-one__twitter-feed-content.owl-carousel .owl-dots .owl-dot {
    position: relative;
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 0%;
    background-color: var(--sopot-white);
    margin: 0px 5px;
    padding: 0px;
    transition: all 100ms linear;
    transition-delay: 0.1s;
}

.footer-widget-one__twitter-feed-content.owl-carousel .owl-dot.active {
    background-color: var(--sopot-base);
}

.footer-widget-one__twitter-feed-content.owl-carousel .owl-dot:focus {
    outline: none;
}

.footer-widget-one__twitter-feed-content.owl-carousel .owl-dots .owl-dot span {
    display: none;
}

/*--------------------------------------------------------------
# CTA One
--------------------------------------------------------------*/
.cta-one {
    position: relative;
    display: block;
    background-color: var(--sopot-base);
    padding: 101px 0;
    z-index: 1;
}

.cta-one__inner {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.cta-one__left {
    position: relative;
    display: flex;
    align-items: center;
}

.cta-one__icon {
    position: relative;
    height: 125px;
    width: 125px;
    border-radius: 50%;
    font-size: 65px;
    background-color: var(--sopot-white);
    color: var(--sopot-base);
    display: flex;
    align-items: center;
    justify-content: center;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
    z-index: 1;
}

.cta-one__icon:hover {
    color: var(--sopot-white);
}

.cta-one__icon:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    border-radius: 50%;
    background-color: var(--sopot-black);
    transform: scale(0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.62, 0.21, 0.45, 1.52);
    z-index: -1;
}

.cta-one__icon:hover:before {
    transform: scaleX(1);
}

.cta-one__title {
    font-size: 40px;
    color: var(--sopot-white);
    line-height: 50px;
    font-weight: 500;
    margin-left: 40px;
}

.cta-one__right {
    position: relative;
    display: block;
}

.cta-one__btn {
    background-color: var(--sopot-black);
}

.cta-one__btn:hover {
    color: var(--sopot-black);
}

.cta-one__btn:after,
.cta-one__btn:before {
    background-color: var(--sopot-white);
}

/*--------------------------------------------------------------
# Blog Three
--------------------------------------------------------------*/
.blog-three {
    position: relative;
    display: block;
    background: #ffffff;
    padding-top: 120px;
    padding-bottom: 90px;
    z-index: 10;
}

.blog-three::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 586px;
    background-color: rgba(var(--sopot-base-rgb), .10);
    content: "";
    z-index: -1;
}

.blog-three__single {
    position: relative;
    display: block;
    margin-bottom: 30px;
}

.blog-three__img {
    position: relative;
    display: block;
    overflow: hidden;
    z-index: 1;
}

.blog-three__img img {
    width: 100%;
}

.blog-three__img::before {
    position: absolute;
    content: '';
    background-color: var(--sopot-black);
    width: 0%;
    height: 100%;
    left: 0px;
    top: 0px;
    opacity: .3;
    z-index: 1;
    transition: all 500ms ease;
}

.blog-three__img::after {
    position: absolute;
    content: '';
    background-color: var(--sopot-black);
    width: 0%;
    height: 100%;
    right: 0px;
    top: 0px;
    opacity: .3;
    z-index: 1;
    transition: all 500ms ease;
}

.blog-three__single:hover .blog-three__img::before,
.blog-three__single:hover .blog-three__img::after {
    width: 50%;
}

.blog-three__content {
    position: relative;
    display: block;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 5px 13px 0px rgba(0, 0, 0, 0.12);
    padding: 30px 30px 35px;
}

.blog-three__content:before {
    position: absolute;
    bottom: 0px;
    left: 0px;
    right: 0px;
    content: "";
    height: 3px;
    background-color: var(--sopot-base);
    transition: transform 500ms ease;
    transform: scale(0, 1);
    transform-origin: left center;
}

.blog-three__single:hover .blog-three__content:before {
    transform: scale(1, 1);
    transform-origin: right center;
}

.blog-three__meta {
    position: relative;
    display: flex;
    align-items: center;
}

.blog-three__meta li+li {
    margin-left: 15px;
}

.blog-three__meta li a {
    font-size: 16px;
    color: var(--sopot-base);
}

.blog-three__meta li a i {
    padding-right: 5px;
}

.blog-three__title {
    font-size: 24px;
    line-height: 34px;
    font-weight: 600;
    margin-top: 20px;
    margin-bottom: 16px;
}

.blog-three__title a {
    color: var(--sopot-black);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.blog-three__single:hover .blog-three__title a {
    color: var(--sopot-base);
}

.blog-three__person {
    position: relative;
    display: flex;
    align-items: center;
}

.blog-three__person-img {
    position: relative;
    display: block;
    border-radius: 50%;
}

.blog-three__person-img img {
    width: auto;
    border-radius: 50%;
}

.blog-three__person-name {
    margin-left: 20px;
}

.blog-three__person-name p {
    font-size: 16px;
    color: var(--sopot-base);
}

.blog-three__person-name p span {
    color: var(--sopot-black);
}




/*--------------------------------------------------------------
# Up Comming Event Two
--------------------------------------------------------------*/
.up-comming-event-two {
    position: relative;
    display: block;
    background: #ffffff;
    padding: 120px 0 90px;
}

.up-comming-event-two__left {
    position: relative;
    display: block;
}

.up-comming-event-two__top {
    position: relative;
    display: block;
    margin-bottom: 30px;
}

.up-comming-event-two__list {
    position: relative;
    display: flex;
    align-items: center;
    border: 1px solid rgba(var(--sopot-base-rgb), .10);
}

.up-comming-event-two__list li {
    position: relative;
    display: flex;
    align-items: center;
    padding: 24px 39px;
    width: 100%;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
    text-align: center;
    justify-content: center;
    cursor: pointer;
}

.up-comming-event-two__list li:before {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    content: "";
    width: 1px;
    background-color: rgba(var(--sopot-base-rgb), .10);
}

.up-comming-event-two__list li:last-child:before {
    display: none;
}

.up-comming-event-two__list li:hover {}

.up-comming-event-two .tabs-button-box .tab-btn-item:hover,
.up-comming-event-two .tabs-button-box .tab-btn-item.active-btn-item {
    background-color: var(--sopot-base);
}



.up-comming-event-two__list li p {
    font-size: 20px;
    color: var(--sopot-base);
    font-weight: 600;
    text-transform: uppercase;
    line-height: 20px;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.up-comming-event-two__list li p span {
    font-size: 15px;
    font-weight: 400;
}

.up-comming-event-two__list li:hover p {}


.up-comming-event-two .tabs-button-box .tab-btn-item:hover p,
.up-comming-event-two .tabs-button-box .tab-btn-item.active-btn-item p {
    color: var(--sopot-white);
}



.up-comming-event-two .tabs-content-box {
    position: relative;
    display: block;
}

.up-comming-event-two .tab-content-box-item {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: auto;
    visibility: hidden;
}

.tab-content-box-item.tab-content-box-item-active {
    position: relative;
    visibility: visible;
    z-index: 5;
}

.tab-content-box-item .up-comming-event-two__bottom {
    transition: all 0.7s ease;
    transform: scaleX(0);
    opacity: 0;
    transform-origin: left;
}

.tab-content-box-item.tab-content-box-item-active .up-comming-event-two__bottom {
    opacity: 1;
    transform: scaleX(1);
}





.up-comming-event-two__bottom {
    position: relative;
    display: block;
}

.up-comming-event-two__single {
    position: relative;
    display: flex;
    border: 1px solid rgba(var(--sopot-base-rgb), .20);
    align-items: center;
    justify-content: space-between;
    padding: 30px 30px;
    margin-bottom: 30px;
}

.up-comming-event-two__location-box {
    position: relative;
    display: block;
    max-width: 558px;
    width: 100%;
}

.up-comming-event-two__location-title {
    position: relative;
    font-size: 20px;
    font-weight: 400;
    line-height: 30px;
    margin-bottom: 22px;
}

.up-comming-event-two__location-title:before {
    position: absolute;
    bottom: -10px;
    left: 0;
    right: 0;
    height: 1px;
    content: "";
    background-color: rgba(var(--sopot-base-rgb), .20);
}

.up-comming-event-two__location-list {
    position: relative;
    display: block;
}

.up-comming-event-two__location-list li {
    position: relative;
    display: flex;
    align-items: center;
}

.up-comming-event-two__location-list li+li {
    margin-top: 6px;
}

.up-comming-event-two__location-list li .icon {
    position: relative;
    display: flex;
    align-items: center;
}

.up-comming-event-two__location-list li .icon span {
    font-size: 15px;
    color: var(--sopot-base);
}

.up-comming-event-two__location-list li .text {
    margin-left: 10px;
}

.up-comming-event-two__date-box {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 95px;
    width: 120px;
    text-align: center;
    border: 1px solid rgba(var(--sopot-base-rgb), .20);
    flex-direction: column;
}

.up-comming-event-two__date-box p {
    font-size: 35px;
    color: var(--sopot-base);
    font-weight: 600;
    line-height: 35px;
    font-family: var(--sopot-font-two);
}

.up-comming-event-two__date-box span {
    font-size: 20px;
    font-weight: 400;
    color: var(--sopot-base);
}

.up-comming-event-two__right {
    position: relative;
    display: block;
}

.up-comming-event-two__img {
    position: relative;
    display: block;
}

.up-comming-event-two__img img {
    width: 100%;
}

/*--------------------------------------------------------------
# Help One
--------------------------------------------------------------*/
.help-one {
    position: relative;
    display: block;
    padding: 110px 0 44px;
    z-index: 1;
}

.help-one-bg {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: -1;
}

.help-one-bg:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    background-color: rgba(var(--sopot-base-rgb), .90);
}

.help-one__title {
    position: relative;
    display: block;
    text-align: center;
    font-size: 40px;
    color: var(--sopot-white);
    line-height: 50px;
    margin-bottom: 51px;
}

.help-one__content-box {
    position: relative;
    display: block;
}

.help-one__single {
    position: relative;
    display: block;
    padding-left: 130px;
    margin-bottom: 72px;
}

.help-one__icon {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100px;
    width: 100px;
    border: 2px solid var(--sopot-white);
    border-radius: 50%;
    top: -3px;
    left: 0;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
    z-index: 1;
}

.help-one__single:hover .help-one__icon {
    border: 2px solid transparent;
}

.help-one__icon:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    border-radius: 50%;
    background-color: var(--sopot-white);
    transform: scale(0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.62, 0.21, 0.45, 1.52);
    z-index: -1;
}

.help-one__single:hover .help-one__icon:before {
    transform: scaleX(1);
}

.help-one__icon span {
    position: relative;
    display: inline-block;
    font-size: 50px;
    color: var(--sopot-white);
    transition: all 500ms linear;
    transition-delay: 0.1s;
    transform: scale(1);
}

.help-one__single:hover .help-one__icon span {
    color: var(--sopot-base);
    transform: scale(.9);
}

.help-one__content {
    position: relative;
    display: block;
}

.help-one__content h4 {
    font-size: 22px;
    color: var(--sopot-white);
    line-height: 32px;
    font-weight: 600;
    margin-bottom: 9px;
}

.help-one__content p {
    color: var(--sopot-white);
}

/*--------------------------------------------------------------
# Testimonial Two
--------------------------------------------------------------*/
.testimonial-two {
    position: relative;
    display: block;
    padding: 120px 0 215px;
}

.testimonial-two__carousel {
    position: relative;
    display: block;
}

.testimonial-two__single {
    position: relative;
    display: flex;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 0px 35px 0px rgba(0, 0, 0, 0.06);
    padding: 30px 30px 23px;
}

.testimonial-two__img {
    position: relative;
    display: inline-block;
}

.testimonial-two__img img {
    width: auto !important;
}

.testimonial-two__quote {
    position: absolute;
    bottom: 62px;
    left: 50%;
    transform: translateX(-50%);
    height: 40px;
    width: 40px;
    background-color: var(--sopot-base);
    display: flex;
    align-items: center;
    justify-content: center;
}

.testimonial-two__quote span {
    font-size: 20px;
    color: var(--sopot-white);
}

.testimonial-two__content {
    margin-left: 30px;
}

.testimonial-two__star {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 13px;
}

.testimonial-two__star span {
    font-size: 15px;
    color: var(--sopot-base);
}

.testimonial-two__star span.clr-gray {
    color: var(--sopot-gray);
}

.testimonial-two__star span+span {
    margin-left: 10px;
}

.testimonial-two__client-info {
    position: relative;
    display: block;
    border-top: 1px solid rgba(var(--sopot-base-rgb), .10);
    padding-top: 22px;
    margin-top: 23px;
}

.testimonial-two__client-name {
    font-size: 20px;
    line-height: 30px;
}

.testimonial-two__carousel.owl-carousel .owl-stage-outer {
    overflow: visible;

}

.testimonial-two__carousel.owl-carousel .owl-item {
    opacity: 0;
    visibility: hidden;
    transition: opacity 500ms ease, visibility 500ms ease;
}

.testimonial-two__carousel.owl-carousel .owl-item.active {
    opacity: 1;
    visibility: visible;
}

.testimonial-two__carousel.owl-theme .owl-nav {
    position: absolute;
    bottom: -100px;
    left: 0;
    right: 0;
}

.testimonial-two__carousel.owl-theme .owl-nav .owl-next {
    height: 65px;
    width: 65px;
    background-color: transparent;
    border-radius: 50%;
    color: var(--sopot-black);
    font-size: 20px;
    text-align: center;
    border: 2px solid rgba(var(--sopot-base-rgb), .20);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.testimonial-two__carousel.owl-theme .owl-nav .owl-prev {
    height: 65px;
    width: 65px;
    background-color: transparent;
    border-radius: 50%;
    color: var(--sopot-black);
    font-size: 20px;
    text-align: center;
    border: 2px solid rgba(var(--sopot-base-rgb), .20);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.testimonial-two__carousel.owl-theme .owl-nav .owl-next:hover,
.testimonial-two__carousel.owl-theme .owl-nav .owl-prev:hover {
    color: var(--sopot-base);
    border: 2px solid rgba(var(--sopot-base-rgb), 1);
}

/*--------------------------------------------------------------
# Page Header
--------------------------------------------------------------*/
.page-header {
    position: relative;
    display: block;
    padding: 208px 0 213px;
    background-color: var(--sopot-black);
    z-index: 1;
}

.page-header-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    mix-blend-mode: luminosity;
    opacity: .20;
    z-index: -1;
}

.page-header__inner {
    position: relative;
    display: block;
    text-align: center;
    z-index: 15;
}

.page-header__inner h2 {
    font-size: 50px;
    color: var(--sopot-white);
    line-height: 60px;
    font-weight: 700;
    margin-bottom: 11px;
}

.thm-breadcrumb {
    position: relative;
    display: block;
}

.thm-breadcrumb li {
    position: relative;
    display: inline-block;
    color: var(--sopot-white);
    font-size: 17px;
    font-weight: 500;
    letter-spacing: 0.002em;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.thm-breadcrumb li+li {
    margin-left: 5px;
}

.thm-breadcrumb li a {
    position: relative;
    display: inline-block;
    color: var(--sopot-white);
    font-size: 17px;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.thm-breadcrumb li:hover a {
    color: var(--sopot-base);
}

.thm-breadcrumb li.active {
    color: var(--sopot-base);
}

/*--------------------------------------------------------------
# Blog Page V-1
--------------------------------------------------------------*/
.blog-page-V-1 {
    position: relative;
    display: block;
    padding: 120px 0 120px;
}

/*--------------------------------------------------------------
# Blog Page V-2
--------------------------------------------------------------*/
.blog-page-v-2 {
    position: relative;
    display: block;
    padding: 120px 0 120px;
}

.blog-page__pagination {
    position: relative;
    display: block;
    padding: 30px 0 0px;
}

.blog-page__pagination .pg-pagination li {
    display: inline-block;
    margin-right: 6px;
}

.blog-page__pagination .pg-pagination li:last-child {
    margin-right: 0;
}

.blog-page__pagination .pg-pagination li a {
    height: 50px;
    width: 60px;
    text-align: center;
    line-height: 50px;
    display: inline-block;
    color: #433838;
    background-color: #faf6f6;
    border-top-right-radius: 7px;
    font-weight: 500;
    font-size: 16px;
    font-family: var(--sopot-font-two);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.blog-page__pagination .pg-pagination li a:hover {
    background-color: var(--sopot-base);
    color: var(--sopot-white);
}


/*--------------------------------------------------------------
# Blog Sidebar
--------------------------------------------------------------*/
.blog-sidebar {
    position: relative;
    display: block;
    padding: 120px 0 120px;
}

.blog-sidebar__left {
    position: relative;
    display: block;
}

.blog-sidebar__content-box {
    position: relative;
    display: block;
}

.blog-sidebar__single {
    position: relative;
    display: block;
}

.blog-sidebar__single+.blog-sidebar__single {
    margin-top: 30px;
}

.blog-sidebar__img {
    position: relative;
    display: block;
    overflow: hidden;
}

.blog-sidebar__img img {
    width: 100%;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.blog-sidebar__single:hover .blog-sidebar__img img {
    transform: scale(1.05);
}

.blog-sidebar__img>a {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(var(--sopot-black-rgb), 0.3);
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 30px;
    color: var(--sopot-base);
    transition: opacity 500ms ease, visibility 500ms ease, transform 500ms ease;
    visibility: hidden;
    opacity: 0;
    transform: translateY(-30%);
}

.blog-sidebar__img>a>span {
    position: relative;
}

.blog-sidebar__img>a>span::before {
    content: "";
    width: 20px;
    height: 2px;
    background-color: var(--sopot-white);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: all 500ms ease;
}

.blog-sidebar__img>a>span::after {
    content: "";
    transition: all 500ms ease;
    width: 2px;
    height: 20px;
    background-color: var(--sopot-white);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.blog-sidebar__img>a:hover>span::before,
.blog-sidebar__img>a:hover>span::after {
    background-color: var(--sopot-base);
}

.blog-sidebar__single:hover .blog-sidebar__img>a {
    visibility: visible;
    transform: translateY(0%);
    opacity: 1;
}


.blog-sidebar__content {
    position: relative;
    display: block;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 5px 13px 0px rgba(0, 0, 0, 0.12);
    padding: 35px 30px 27px;
}

.blog-sidebar__meta {
    position: relative;
    display: flex;
    align-items: center;
}

.blog-sidebar__cat {
    position: relative;
    display: inline-block;
    padding: 7px 10px 4px;
    z-index: 1;
}

.blog-sidebar__cat:before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    content: "";
    background-color: rgba(var(--sopot-base-rgb), .20);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
    z-index: -1;
}

.blog-sidebar__single:hover .blog-sidebar__cat:before {
    background-color: rgba(var(--sopot-base-rgb), 1);
}

.blog-sidebar__cat p {
    font-size: 14px;
    color: var(--sopot-base);
    line-height: 14px;
    text-transform: uppercase;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.blog-sidebar__single:hover .blog-sidebar__cat p {
    color: var(--sopot-white);
}

.blog-sidebar__date {
    position: relative;
    display: flex;
    align-items: center;
    margin-left: 20px;
}

.blog-sidebar__date p {
    font-size: 14px;
    color: var(--sopot-base);
    line-height: 14px;
}

.blog-sidebar__date p span {
    padding-right: 5px;
}

.blog-sidebar__title {
    font-size: 25px;
    font-weight: 700;
    line-height: 35px;
    margin-top: 13px;
    margin-bottom: 13px;
}

.blog-sidebar__title a {
    color: var(--sopot-black);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.blog-sidebar__single:hover .blog-sidebar__title a {
    color: var(--sopot-base);
}

.blog-sidebar__text {
    font-size: 16px;
    line-height: 26px;
}

.blog-sidebar .blog-page__pagination {
    padding: 60px 0 0px;
}

/*--------------------------------------------------------------
# Sidebar
--------------------------------------------------------------*/

@media (max-width: 991px) {
    .sidebar {
        margin-top: 50px;
    }
}

.sidebar__single+.sidebar__single {
    margin-top: 30px;
}

.sidebar__title {
    margin: 0;
    font-size: 25px;
    margin-bottom: 28px;
    font-weight: 700;
}

.sidebar__search {
    position: relative;
    display: block;
    background-color: rgba(var(--sopot-base-rgb), .20);
    padding: 30px;
}

.sidebar__search-form {
    position: relative;
}

.sidebar__search-form input[type="search"] {
    display: block;
    border: none;
    outline: none;
    background-color: var(--sopot-base);
    color: var(--sopot-white);
    font-size: 16px;
    font-weight: 500;
    padding-left: 20px;
    height: 60px;
    width: 100%;
    padding-right: 60px;
}

.sidebar__search-form ::-webkit-input-placeholder {
    color: var(--sopot-white);
    opacity: 1;
}

.sidebar__search-form ::-moz-placeholder {
    color: var(--sopot-white);
    opacity: 1;
}

.sidebar__search-form :-ms-input-placeholder {
    color: var(--sopot-white);
    opacity: 1;
}

.sidebar__search-form ::-ms-input-placeholder {
    color: var(--sopot-white);
    opacity: 1;
}

.sidebar__search-form ::placeholder {
    color: var(--sopot-white);
    opacity: 1;
}

.sidebar__search-form :-ms-input-placeholder {
    color: var(--sopot-white);
}

.sidebar__search-form ::-ms-input-placeholder {
    color: var(--sopot-white);
}

.sidebar__search-form button[type="submit"] {
    background-color: var(--sopot-white);
    color: var(--sopot-base);
    font-size: 18px;
    position: absolute;
    top: 5px;
    right: 0;
    bottom: 5px;
    width: 50px;
    outline: none;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.sidebar__search-form:hover button[type="submit"] {
    background-color: var(--sopot-black);
    color: var(--sopot-white);
}

.sidebar__category {
    position: relative;
    display: block;
    background-color: rgba(var(--sopot-base-rgb), .20);
    padding: 35px 30px 40px;
}

.sidebar__category-list {
    margin: 0;
}

.sidebar__category-list li+li {
    margin-top: 20px;
}

.sidebar__category-list li a {
    color: var(--sopot-black);
    font-size: 16px;
    font-weight: 600;
    position: relative;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
    display: block;
    padding: 16.5px 20px;
    background-color: var(--sopot-white);
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
}

.sidebar__category-list li a:hover {
    background-color: var(--sopot-base);
    color: var(--sopot-white);
}

.sidebar__category-list li.active a {
    background-color: var(--sopot-base);
    color: var(--sopot-white);
}

.sidebar__category-list li a span {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 60px;
    right: 0;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
    color: var(--sopot-white);
    background-color: var(--sopot-base);
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
    font-weight: 500;
}

.sidebar__category-list li a:hover span {
    background-color: var(--sopot-white);
    color: var(--sopot-base);
}

.sidebar__category-list li.active a span {
    background-color: var(--sopot-white);
    color: var(--sopot-base);
}


.sidebar__post {
    position: relative;
    display: block;
    padding: 35px 30px 32px;
    background-color: rgba(var(--sopot-base-rgb), .20);
}

.sidebar__post-single+.sidebar__post-single {
    margin-top: 22px;
}

.sidebar__post .sidebar__title {
    margin-left: 20px;
}

.sidebar__post-list {
    margin: 0;
}

.sidebar__post-list li {
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    padding: 12px 20px 13px;
    transition: all 500ms ease;
    border-radius: var(--sopot-bdr-radius);
}

.sidebar__post-list li:hover {
    background-color: rgb(255, 255, 255);
}

.sidebar__post-list li+li {
    margin-top: 10px;
}

.sidebar__post-image {
    margin-right: 20px;
}

.sidebar__post-image>img {
    width: 70px;
}

.sidebar__post-content {
    position: relative;
    top: 0;
}

.sidebar__post-content h3 {
    font-size: 16px;
    margin: 0;
    line-height: 27px;
}

.sidebar__post-content-meta {
    font-size: 14px;
    font-weight: 400;
    color: var(--sopot-base) !important;
    font-family: var(--sopot-font);
    transition: all 500ms ease;
}

.sidebar__post-content-meta i {
    color: var(--sopot-gray);
    font-size: 12px;
    padding-right: 3px;
}

.sidebar__post-content h3 a {
    color: var(--sopot-black);
    transition: all 500ms ease;
    margin-top: 1px;
    display: block;
    font-size: 16px;
}


.sidebar__support {
    position: relative;
    display: block;
    background-color: var(--sopot-base);
    text-align: center;
    padding: 77px 30px 90px;
    z-index: 1;
}

.sidebar__support-bg {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    mix-blend-mode: luminosity;
    opacity: .1;
    z-index: -1;
}

.sidebar__suppot-title {
    font-size: 50px;
    color: var(--sopot-white);
    font-weight: 500;
    line-height: 60px;
}

.sidebar__suppot-text {
    color: var(--sopot-white);
    margin: 0;
    padding-top: 20px;
    padding-bottom: 33px;
}

.sidebar__support-btn {
    border: 0;
    background-color: var(--sopot-white);
    color: var(--sopot-base);
}

.sidebar__tags {
    position: relative;
    display: block;
    background-color: rgba(var(--sopot-base-rgb), .20);
    padding: 35px 25px 40px;
}

.sidebar__tags-list {
    margin-top: -10px;
}

.sidebar__tags-list a {
    font-size: 12px;
    color: #7b848b;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
    background: var(--sopot-white);
    display: inline-block;
    padding: 5px 20px;
    margin-left: 5px;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.1em;
}

.sidebar__tags-list a+a {
    margin-left: 5px;
    margin-top: 10px;
}

.sidebar__tags-list a:hover {
    color: var(--sopot-white);
    background: var(--sopot-base);
}

.sidebar__comments {
    position: relative;
    display: block;
    background-color: rgba(var(--sopot-base-rgb), .20);
    padding: 45px 30px 43px;
}

.sidebar__comments .sidebar__title {
    margin-bottom: 24px;
}

.sidebar__comments-list {
    position: relative;
    display: block;
}

.sidebar__comments-list li {
    position: relative;
    display: block;
    padding-left: 65px;
}

.sidebar__comments-list li+li {
    margin-top: 23px;
}

.sidebar__comments-icon {
    height: 45px;
    width: 45px;
    background-color: var(--sopot-white);
    border-radius: 50%;
    font-size: 15px;
    color: var(--sopot-black);
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
}

.sidebar__comments-list li:hover .sidebar__comments-icon {
    background-color: var(--sopot-base);
    color: var(--sopot-white);
}

.sidebar__comments-text-box p {
    font-size: 15px;
    margin: 0;
    line-height: 26px;
}

.sidebar__comments-text-box h5 {
    font-size: 16px;
    color: var(--sopot-black);
    line-height: 26px;
    font-family: var(--sopot-font);
    font-weight: 400;
}

/*--------------------------------------------------------------
# Blog Details
--------------------------------------------------------------*/
.blog-details {
    position: relative;
    display: block;
    padding: 100px 0 100px;
}

.blog-details__left {
    position: relative;
    display: block;
}

.blog-details__img {
    position: relative;
    display: block;
}

.blog-details__img img {
    width: 100%;
}

.blog-details__date {
    position: absolute;
    top: 0;
    left: 0;
    background-color: var(--sopot-base);
    text-align: center;
    padding: 15px 20px;
}

.blog-details__date p {
    font-size: 15px;
    color: var(--sopot-white);
    margin: 0;
    line-height: 20px;
    text-transform: uppercase;
    font-weight: 700;
}

.blog-details__content {
    position: relative;
    display: block;
    margin-top: 29px;
}

.blog-details__meta {
    position: relative;
    display: flex;
    align-items: center;
}

.blog-details__meta li+li {
    margin-left: 20px;
}

.blog-details__meta li a {
    font-size: 15px;
    color: var(--sopot-base);
}

.blog-details__meta li a span {
    position: relative;
    top: -1px;
    margin-left: 6px;
}

.blog-details__title {
    font-size: 30px;
    line-height: 40px;
    font-weight: 600;
    margin-top: 18px;
    margin-bottom: 18px;
}

.blog-details__text-1 {
    margin: 0;
}

.blog-details__text-2 {
    margin: 0;
    padding-top: 40px;
}

.blog-details__content-two {
    position: relative;
    display: block;
    margin-top: 68px;
}

.blog-details__content-two-img {
    position: relative;
    display: block;
}

.blog-details__content-two-img img {
    width: 100%;
}

.blog-details__content-two-details {
    position: relative;
    display: block;
    margin-top: 25px;
}

.blog-details__content-two-title {
    font-size: 30px;
    line-height: 40px;
    margin-bottom: 18px;
}

.blog-details__content-two-text-1 {
    margin: 0;
}

.blog-details__content-two-text-2 {
    margin: 0;
    padding-top: 40px;
}

.blog-details__content-two-points {
    position: relative;
    display: block;
    margin-top: 39px;
}

.blog-details__content-two-points li {
    position: relative;
    display: block;
    padding-left: 24px;
}

.blog-details__content-two-points li:before {
    position: absolute;
    top: 10px;
    left: 0;
    height: 10px;
    width: 10px;
    background-color: var(--sopot-base);
    border-radius: 50%;
    content: "";
}

.blog-details__content-two-points li+li {
    margin-top: 15px;
}

.blog-details__bottom {
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 30px 0 30px;
    margin-top: 51px;
    border-top: 1px solid #dddddd;
}

.blog-details__bottom p {
    margin: 0;
}

.blog-details__tags span {
    color: var(--sopot-black);
    font-size: 20px;
    margin-right: 16px;
    font-weight: 700;
}

.blog-details__tags a {
    position: relative;
    color: var(--sopot-white);
    font-size: 12px;
    background-color: var(--sopot-base);
    display: inline-block;
    padding: 7px 20px;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-weight: 700;
    border-radius: var(--sopot-bdr-radius);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.blog-details__tags a:hover {
    background-color: var(--sopot-black);
    color: var(--sopot-white);
}

.blog-details__tags a+a {
    margin-left: 6px;
}

.blog-details__social-list {
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
}

.blog-details__social-list a {
    position: relative;
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    text-align: center;
    color: var(--sopot-white);
    background-color: var(--sopot-base);
    border-radius: 5%;
    font-size: 15px;
    height: 35px;
    width: 35px;
    transform: rotate(0);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
    z-index: 1;
}

.blog-details__social-list a:hover {
    color: var(--sopot-white);
    transform: rotate(360deg);
    background-color: var(--sopot-base);
}

.blog-details__social-list a:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    background-color: var(--sopot-black);
    border-radius: 5%;
    transform: scale(0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.62, 0.21, 0.45, 1.52);
    z-index: -1;
}

.blog-details__social-list a+a {
    margin-left: 10px;
}

.blog-details__social-list a:hover:before {
    transform: scaleX(1);
}

.author-one {
    background-color: #efefef;
    padding: 60px 60px 60px;
    display: -webkit-box;
    display: flex;
    margin-bottom: 53px;
}

.author-one__image {
    position: relative;
    display: inline-block;
}

.author-one__image img {
    width: auto;
    border-radius: var(--sopot-bdr-radius);
}

.author-one__content {
    margin-left: 40px;
    margin-top: -8px;
}

.author-one__content h3 {
    font-size: 24px;
    line-height: 34px;
    font-weight: 700;
    margin-bottom: 25px
}

.author-one__content p {
    margin: 0;
}

/*--------------------------------------------------------------
## Comments
--------------------------------------------------------------*/

.comment-one__title,
.comment-form__title {
    margin: 0;
    color: var(--sopot-black);
    font-size: 30px;
    text-transform: uppercase;
    font-weight: 700;
    margin-bottom: 52px;
}

.comment-one__single {
    display: -webkit-box;
    display: flex;
    border-bottom: 1px solid #dddddd;
    padding-bottom: 53px;
    margin-bottom: 60px;
}

.comment-one__content {
    position: relative;
    margin-left: 43px;
}

.comment-one__content h3 {
    margin: 0;
    font-size: 20px;
    color: var(--sopot-black);
    margin-bottom: 27px;
    font-weight: 700;
}

.comment-one__content p {
    margin: 0;
    margin-bottom: 15px;
}

.comment-one__content span {
    color: var(--sopot-base);
    font-weight: 500;
}

.comment-one__btn {
    font-size: 12px;
    padding: 5px 30px;
    position: absolute;
    top: 0;
    right: 0;
    text-transform: uppercase;
}

.comment-one__btn:hover {
    color: var(--sopot-white);
}

.comment-one__image {
    position: relative;
    display: block;
    border-radius: 50%;
}

.comment-one__image img {
    border-radius: 50%;
}

.comment-form {
    position: relative;
    display: block;
}

.comment-form .comment-form__title {
    margin-top: -8px;
}

.comment-one__form {
    position: relative;
    display: block;
}

.comment-form__input-box {
    position: relative;
    display: block;
    margin-bottom: 30px;
}

.comment-form__input-box input[type="text"],
.comment-form__input-box input[type="email"] {
    height: 50px;
    width: 100%;
    background-color: transparent;
    border: 1px solid #ebebeb;
    outline: none;
    font-size: 17px;
    color: var(--sopot-gray);
    border-radius: 0;
    padding: 0 20px;
}

.comment-form__input-box textarea {
    width: 100%;
    height: 150px;
    background-color: transparent;
    border: 1px solid #ebebeb;
    outline: none;
    font-size: 17px;
    color: var(--sopot-gray);
    border-radius: 0;
    padding: 22px 30px 30px;
}

.comment-form__textarea {
    height: 150px;
}

.comment-form__btn {
    border: none;
}

/*--------------------------------------------------------------
# Project Details
--------------------------------------------------------------*/
.project-details {
    position: relative;
    display: block;
    padding: 120px 0 0;
}

.project-details__left {
    position: relative;
    display: block;
}

.project-details__img {
    position: relative;
    display: block;
}

.project-details__img img {
    width: 100%;
}

.project-details__right {
    position: relative;
    display: block;
}

.project-details__title {
    font-size: 25px;
    line-height: 25px;
    margin-bottom: 35px;
}

.project-details__list {
    position: relative;
    display: block;
}

.project-details__list li {
    position: relative;
    display: flex;
    align-items: center;
    border: 1px solid #ebebeb;
    padding: 11px 30px 11px;
    overflow: hidden;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.project-details__list li:hover {
    border: 1px solid var(--sopot-base);
}

.project-details__list li+li {
    margin-top: 30px;
}

.project-details__list li .left {
    position: relative;
    display: block;
    max-width: 115px;
    width: 100%;
    float: left;
}

.project-details__list li .left h4 {
    font-size: 17px;
    color: var(--sopot-black);
    font-weight: 500;
}

.project-details__list li .right {
    position: relative;
    display: block;
    max-width: 210px;
    width: 100%;
    float: left;
    margin-left: 100px;
}

.project-details__social {
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
}

.project-details__social a {
    position: relative;
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    text-align: center;
    color: var(--sopot-white);
    background-color: var(--sopot-base);
    border-radius: 5%;
    font-size: 15px;
    height: 35px;
    width: 35px;
    transform: rotate(0);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
    z-index: 1;
}

.project-details__social a:hover {
    color: var(--sopot-white);
    transform: rotate(360deg);
    background-color: var(--sopot-base);
}

.project-details__social a:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    background-color: var(--sopot-black);
    border-radius: 5%;
    transform: scale(0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.62, 0.21, 0.45, 1.52);
    z-index: -1;
}

.project-details__social a+a {
    margin-left: 10px;
}

.project-details__social a:hover:before {
    transform: scaleX(1);
}

.project-details__bottom {
    position: relative;
    display: block;
    margin-top: 43px;
}

.project-details__text-box {
    position: relative;
    display: block;
}

.project-details__text-1 {
    padding-bottom: 43px;
}

/*--------------------------------------------------------------
# Video Three
--------------------------------------------------------------*/
.video-three {
    position: relative;
    display: block;
    padding: 113px 0 120px;
}

.video-three .video-one__content-box {
    padding: 135px 0 120px;
}

.video-three .video-two-shape {
    right: -195px;
}

/*--------------------------------------------------------------
# Team Details
--------------------------------------------------------------*/
.team-details {
    position: relative;
    display: block;
    padding: 120px 0 0px;
}

.team-details__inner {
    position: relative;
    display: block;
}

.team-details__top-left {
    position: relative;
    display: block;
}

.team-details__top-img {
    position: relative;
    display: block;
}

.team-details__top-img img {
    width: 100%;
}

.team-details__top-right {
    position: relative;
    display: block;
    margin-left: 70px;
}

.team-details__top-content {
    position: relative;
    display: block;
    margin-top: -6px;
}

.team-details__top-name {
    font-size: 40px;
    font-weight: 700;
    line-height: 50px;
    margin-top: 2px;
}

.team-details__top-title {
    font-size: 14px;
    color: var(--sopot-gray);
    font-weight: 500;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    line-height: 24px;
}

.team-details__social {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-top: 19px;
    margin-bottom: 38px;
}

.team-details__social a {
    position: relative;
    height: 40px;
    width: 40px;
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    text-align: center;
    color: var(--sopot-gray);
    font-size: 15px;
    border-radius: 0%;
    overflow: hidden;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
    border: 1px solid var(--sopot-gray);
    z-index: 1;
}

.team-details__social a:hover {
    color: var(--sopot-white);
    border: 1px solid var(--sopot-base);
}

.team-details__social a:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    background-color: var(--sopot-base);
    border-radius: 0%;
    transform: scale(0);
    transform-origin: center;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.62, 0.21, 0.45, 1.52);
    z-index: -1;
}

.team-details__social a:hover:before {
    transform: scaleX(1);
}

.team-details__social a+a {
    margin-left: 10px;
}

.team-details__top-text-1 {
    font-size: 30px;
    color: var(--sopot-base);
    line-height: 46px;
}

.team-details__top-text-2 {
    font-size: 18px;
    line-height: 34px;
    padding-top: 33px;
    padding-bottom: 35px;
}

.team-details__top-text-3 {
    font-size: 18px;
    line-height: 34px;
}



















/*--------------------------------------------------------------
# Join Team
--------------------------------------------------------------*/
.join-team {
    position: relative;
    display: block;
    padding: 120px 0 120px;
}

.join-team__Left {
    position: relative;
    display: block;
}

.join-team__images {
    position: relative;
    display: block;
}

.join-team__img-single {
    position: relative;
    display: block;
    margin-bottom: 30px;
}

.join-team__img-single img {
    width: 100%;
    border-radius: var(--sopot-bdr-radius);
}

.join-team__content {
    position: relative;
    display: block;
}

.join-team__title {
    font-size: 30px;
    line-height: 40px;
    margin-bottom: 20px;
}

.join-team__text {
    margin: 0;
    padding-bottom: 46px;
}

.join-team__points {
    position: relative;
    display: block;
}

.join-team__points li {
    position: relative;
    display: block;
    font-size: 20px;
    color: var(--sopot-black);
}

.join-team__points li+li {
    margin-top: 10px;
}

.join-team__contact {
    position: relative;
    display: block;
    border-top: 1px solid rgba(var(--sopot-base-rgb), .10);
    padding-top: 35px;
    margin-top: 44px;
}

.join-team__contact p {
    margin: 0;
}

.join-team__phone {
    font-size: 24px;
    color: var(--sopot-base);
    display: block;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.join-team__phone:hover {
    color: var(--sopot-black);
}

.join-team__email {
    font-size: 16px;
    display: block;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.join-team__email:hover {
    color: var(--sopot-base);
}

.join-team__right {
    position: relative;
    display: block;
}

.join-team__form {
    position: relative;
    display: block;
}

.join-team__input {
    position: relative;
    display: block;
    margin-bottom: 30px;
}

.join-team__input input[type="text"],
.join-team__input input[type="email"] {
    height: 64px;
    width: 100%;
    border: none;
    outline: none;
    background-color: rgba(var(--sopot-base-rgb), .10);
    color: var(--sopot-gray);
    font-size: 14px;
    padding: 0 30px;
}

.join-team__input textarea {
    height: 180px;
    width: 100%;
    border: none;
    outline: none;
    background-color: rgba(var(--sopot-base-rgb), .10);
    color: var(--sopot-gray);
    font-size: 14px;
    padding: 18px 30px 30px;
}

.join-team__input.join-team__message-box {
    height: 180px;
}

.join-team__btn {
    border: none;
}

#ui-datepicker-div.ui-widget-content {
    background: #fff none repeat scroll 0 0;
    border: 1px solid var(--sopot-black);
    color: #252525;
    font-size: 14px;
    border-radius: 0px;
    width: 270px;
    padding: 5px;
}

#ui-datepicker-div.ui-datepicker .ui-datepicker-header {
    background: var(--sopot-base) none repeat scroll 0 0;
    border: 2px solid var(--sopot-base);
    border-radius: 0;
    color: var(--sopot-white);
    font-weight: 700;
    padding: 5px 0;
    position: relative;
}

.ui-datepicker td a {
    color: var(--sopot-black) !important;
    text-align: center;
    background-image: none !important;
    background: #f5f5f5 !important;
    border: 1px solid #f9f9f9 !important;
}

.ui-state-highlight,
.ui-widget-content .ui-state-highlight,
.ui-widget-header .ui-state-highlight {
    border: 1px solid var(--sopot-black) !important;
    background: var(--sopot-black) !important;
    color: var(--sopot-white) !important;
}

.ui-datepicker .ui-datepicker-prev {
    left: 5px;
}

.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
    border-radius: 30%;
    height: 20px;
    position: absolute;
    top: 7px;
    width: 20px;
    transition: all 500ms ease;
    cursor: pointer;
}

.ui-datepicker .ui-datepicker-next {
    right: 5px;
}

.ui-datepicker .ui-datepicker-prev:hover,
.ui-datepicker .ui-datepicker-next:hover {
    top: 7px;
    background: var(--sopot-white);
    border: none;
}

.ui-datepicker table {
    border-collapse: collapse;
    font-size: 13px;
    margin: 0 0 0.4em;
    width: 100%;
}

.ui-datepicker th {
    border: medium none;
    font-weight: 600;
    padding: 2px 3px;
    text-align: center;
}

.ui-datepicker-calendar .ui-state-default:hover,
.ui-datepicker-calendar .ui-state-default:active {
    background: var(--sopot-black);
    color: var(--sopot-white);
    border-color: var(--sopot-black);
}

#ui-datepicker-div.ui-widget {
    font-family: var(--sopot-font);
}

/*--------------------------------------------------------------
# Causes Three
--------------------------------------------------------------*/
.causes-three {
    position: relative;
    display: block;
}

.causes-three:after {
    display: none;
}

/*--------------------------------------------------------------
# Contact Page
--------------------------------------------------------------*/
.contact-page {
    position: relative;
    display: block;
    padding: 120px 0 140px;
    z-index: 1;
}

.contact-page-bg {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    opacity: 0.02;
    z-index: -1;
}

.contact-page__form {
    position: relative;
    display: block;
}

.contact-page__form .text-message-box {
    height: 200px;
}

.contact-page__form .comment-form__input-box textarea {
    height: 200px;
}

.contact-page__right {
    position: relative;
    display: block;
}

.contact-page__img {
    position: relative;
    display: block;
    z-index: 2;
}

.contact-page__img>img {
    width: 100%;
}

.contact-page__img-shape {
    position: absolute;
    bottom: -20px;
    right: -20px;
    z-index: -1;
}

.contact-page__img-shape>img {
    width: 100%;
}

/*--------------------------------------------------------------
## Contact Info
--------------------------------------------------------------*/
.contact-info {
    position: relative;
    display: block;
    z-index: 2;
}

.contact-info__single {
    position: relative;
    display: block;
    background-color: var(--sopot-base);
    padding: 43px 60px 43px;
    margin-bottom: 30px;
    border-radius: 10px;
}

.contact-info__title {
    font-size: 24px;
    color: var(--sopot-white);
    line-height: 34px;
    margin-bottom: 20px;
}

.contact-info__text {
    font-size: 16px;
    color: var(--sopot-white);
    line-height: 30px;
    margin: 0;
}

.contact-info__email-phone {
    font-size: 16px;
    line-height: 30px;
    margin: 0;
}

.contact-info__email {
    color: var(--sopot-white);
    display: block;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.contact-info__email:hover {
    color: var(--sopot-black);
}

.contact-info__phone {
    color: var(--sopot-white);
    display: block;
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.contact-info__phone:hover {
    color: var(--sopot-black);
}

.contact-info__single-3 {
    padding: 43px 60px 73px;
}

/*--------------------------------------------------------------
# Contact Page Google Map
--------------------------------------------------------------*/
.contact-page-google-map {
    position: relative;
    display: block;
    margin-top: -130px;
}

.contact-page-google-map__one {
    position: relative;
    display: block;
    border: none;
    height: 540px;
    width: 100%;
}

/*--------------------------------------------------------------
# Error Page
--------------------------------------------------------------*/
.error-page {
    position: relative;
    display: block;
    padding: 86px 0 120px;
}

.error-page__inner {
    position: relative;
    display: block;
    text-align: center;
}

.error-page__img {
    position: relative;
    display: block;
}

.error-page__img img {
    width: auto;
}

.error-page__text {
    position: relative;
    display: block;
    color: #606060;
    margin-top: -50px;
    z-index: 22;
}

.error-page__btn-box {
    position: relative;
    display: block;
    margin-top: 43px;
}

/*--------------------------------------------------------------
# About Three
--------------------------------------------------------------*/
.about-three {
    padding: 120px 0 120px;
}

/*--------------------------------------------------------------
# Donations Page
--------------------------------------------------------------*/
.donations-page {
    position: relative;
    display: block;
    padding: 120px 0 90px;
}

/*--------------------------------------------------------------
## Donations List
--------------------------------------------------------------*/
.donations-list {
    position: relative;
    display: block;
    padding: 120px 0 120px;
}

.donations-list__inner {
    position: relative;
    display: block;
}

.donations-list__single {
    position: relative;
    display: block;
}

.donations-list__single+.donations-list__single {
    margin-top: 30px;
}

.donations-list__single .row {
    --bs-gutter-x: 0px;
}

.donations-list__img {
    position: relative;
    display: block;
}

.donations-list__img img {
    width: 100%;
}

.donations-list__right {
    position: relative;
    display: block;
}

.donations-list__content {
    position: relative;
    display: block;
    background-color: rgba(var(--sopot-base-rgb), .10);
    padding: 60px 80px 53px;
}

.donations-list__category {
    position: relative;
    display: inline-block;
    background-color: var(--sopot-base);
    padding: 7px 20px;
    border-radius: 8px;
}

.donations-list__category p {
    font-size: 10px;
    color: var(--sopot-white);
    text-transform: uppercase;
    font-weight: 700;
    letter-spacing: 0.1em;
    margin: 0;
    line-height: 20px;
}

.donations-list__title {
    font-size: 30px;
    line-height: 40px;
    margin-top: 25px;
    margin-bottom: 13px;
}

.donations-list__title a {
    color: var(--sopot-black);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.donations-list__title a:hover {
    color: var(--sopot-base);
}

.donations-list__text {
    font-size: 16px;
    line-height: 30px;
    margin: 0;
}

.donations-list__progress {
    position: relative;
    display: block;
    width: 100%;
    padding-top: 50px;
}

.donations-list__progress .bar {
    position: relative;
    width: 100%;
    height: 4px;
    background: var(--sopot-white);
    border-radius: 0px;
    margin-bottom: 12px;
}

.donations-list__progress .bar-inner {
    position: relative;
    display: block;
    width: 0px;
    height: 4px;
    border-radius: 0px;
    background: var(--sopot-base);
    -webkit-transition: all 1500ms ease;
    -ms-transition: all 1500ms ease;
    -o-transition: all 1500ms ease;
    -moz-transition: all 1500ms ease;
    transition: all 1500ms ease;
}

.donations-list__progress .count-text {
    position: absolute;
    right: -40px;
    bottom: 16px;
    color: var(--sopot-white);
    line-height: 20px;
    font-size: 10px;
    text-align: center;
    font-weight: 400;
    opacity: 0;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
    -moz-transition: all 500ms ease;
    transition: all 500ms ease;
    height: 20px;
    width: 40px;
    background: var(--sopot-base);
}

.donations-list__progress .bar-inner.counted .count-text {
    opacity: 1;
}

.donations-list__progress .count-text:after {
    content: '';
    position: absolute;
    left: 0;
    top: 100%;
    border-right: 6px solid transparent;
    border-top: 4px solid var(--sopot-base);
}

.donations-list__goals {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.donations-list__goals>p {
    font-size: 14px;
    margin: 0;
}

.donations-list__goals>p>span {
    font-size: 18px;
    color: var(--sopot-black);
    font-weight: 500;
    line-height: 28px;
}

/*--------------------------------------------------------------
## Donate Now
--------------------------------------------------------------*/
.donate-now {
    position: relative;
    display: block;
    padding: 120px 0 120px;
}

.donate-now__left {
    position: relative;
    display: block;
    margin-top: -9px;
}

.donate-now__enter-donation {
    position: relative;
    display: block;
}

.donate-now__title {
    font-size: 30px;
    line-height: 40px;
    margin-bottom: 31px;
}

.donate-now__enter-donation-input {
    position: relative;
    display: block;
}

.donate-now__enter-donation-input input[type="text"] {
    height: 135px;
    width: 100%;
    border: none;
    outline: none;
    padding-left: 30px;
    padding-right: 160px;
    font-size: 30px;
    font-weight: 500;
    color: var(--sopot-gray);
    font-family: var(--sopot-font);
    background-color: rgba(var(--sopot-base-rgb), .10);
}

.donate-now__enter-donation-input .bootstrap-select:not([class*="col-"]):not([class*="form-control"]):not(.input-group-btn) {
    position: absolute;
    width: auto !important;
    right: 0;
    top: 0;
    margin-top: 0;
}

.donate-now__enter-donation-input .bootstrap-select>.dropdown-toggle::after {
    display: none;
}

.donate-now__enter-donation-input .bootstrap-select>.dropdown-toggle {
    position: relative;
    height: 135px;
    width: 152px;
    outline: none;
    border-radius: 0px;
    border: 0;
    background-color: var(--sopot-base);
    margin: 0;
    padding: 0;
    color: var(--sopot-white);
    font-size: 70px;
    line-height: 135px;
    font-weight: 700;
    box-shadow: none;
    background-repeat: no-repeat;
    font-family: var(--sopot-font);
    text-align: center;
}

.donate-now__enter-donation-input .bootstrap-select .dropdown-toggle .filter-option {
    text-align: center;
}

.donate-now__personal-info-box {
    position: relative;
    display: block;
    margin-top: 51px;
    margin-bottom: 51px;
}

.donate-now__personal-info-form {
    position: relative;
    display: block;
}

.donate-now__personal-info-input {
    position: relative;
    display: block;
    margin-bottom: 30px;
}

.donate-now__personal-info-input input[type="text"],
.donate-now__personal-info-input input[type="email"] {
    height: 64px;
    width: 100%;
    border: none;
    outline: none;
    background-color: rgba(var(--sopot-base-rgb), .10);
    color: var(--sopot-gray);
    font-size: 14px;
    padding: 0 30px;
    border-radius: 0px;
}

.donate-now__personal-info-input textarea {
    height: 180px;
    width: 100%;
    border: none;
    outline: none;
    background-color: rgba(var(--sopot-base-rgb), .10);
    color: var(--sopot-gray);
    font-size: 14px;
    padding: 18px 30px 30px;
    border-radius: 0px;
}

.donate-now__personal-info-input.donate-now__personal-info-message-box {
    height: 180px;
    margin-bottom: 0;
}


.donate-now__personal-info-form .bootstrap-select:not([class*="col-"]):not([class*="form-control"]):not(.input-group-btn) {
    position: relative;
    display: block;
    width: 100% !important;

}

.donate-now__personal-info-form .bootstrap-select>.dropdown-toggle::after {
    display: none;
}

.donate-now__personal-info-form .bootstrap-select>.dropdown-toggle {
    position: relative;
    height: 64px;
    outline: none !important;
    border-radius: 0px;
    border: 0;
    background-color: rgba(var(--sopot-base-rgb), .10) !important;
    margin: 0;
    padding: 0;
    padding-left: 30px;
    padding-right: 30px;
    color: var(--sopot-gray) !important;
    font-size: 14px;
    line-height: 64px;
    font-weight: 400;
    box-shadow: none !important;
    background-repeat: no-repeat;
    background-size: 14px 12px;
    background-position: right 25.75px center;
}

.donate-now__personal-info-form .bootstrap-select>.dropdown-toggle:before {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 26px;
    font-family: "Font Awesome 5 Free";
    content: "\f107";
    font-weight: 900;
    font-size: 14px;
    color: var(--sopot-gray);
}

.donate-now__payment-info {
    position: relative;
    display: block;
}

.donate-now__payment-info-form {
    position: relative;
    display: block;
}

.donate-now__payment-info-input {
    position: relative;
    display: block;
    margin-bottom: 30px;
}

.donate-now__payment-info-input input[type="text"] {
    height: 64px;
    width: 100%;
    border: none;
    outline: none;
    background-color: rgba(var(--sopot-base-rgb), .10);
    color: var(--sopot-gray);
    font-size: 14px;
    padding: 0 30px;
    border-radius: 0px;
}

.donate-now__payment-info-form .bootstrap-select:not([class*="col-"]):not([class*="form-control"]):not(.input-group-btn) {
    position: relative;
    display: block;
    width: 100% !important;

}

.donate-now__payment-info-form .bootstrap-select>.dropdown-toggle::after {
    display: none;
}

.donate-now__payment-info-form .bootstrap-select>.dropdown-toggle {
    position: relative;
    height: 64px;
    outline: none !important;
    border-radius: 0px;
    border: 0;
    background-color: rgba(var(--sopot-base-rgb), .10) !important;
    margin: 0;
    padding: 0;
    padding-left: 30px;
    padding-right: 30px;
    color: var(--sopot-gray) !important;
    font-size: 14px;
    line-height: 64px;
    font-weight: 400;
    box-shadow: none !important;
    background-repeat: no-repeat;
    background-size: 14px 12px;
    background-position: right 25.75px center;
}

.donate-now__payment-info-form .bootstrap-select>.dropdown-toggle:before {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 26px;
    font-family: "Font Awesome 5 Free";
    content: "\f107";
    font-weight: 900;
    font-size: 14px;
    color: var(--sopot-gray);
}

.donate-now__payment-info-btn {
    border: none;
}


.donate-now__right {
    position: relative;
    display: block;
}

.donate-now__causes {
    position: relative;
    display: block;
    margin-bottom: 30px;
}

.donate-now__causes-img {
    position: relative;
    display: block;
}

.donate-now__causes-img img {
    width: 100%;
}

.donate-now__causes-content-box {
    position: relative;
    display: block;
    background-color: var(--sopot-white);
    border: 1px solid #ebebeb;
    border-top: 0;
}

.donate-now__causes-content {
    position: relative;
    display: block;
    position: relative;
    display: block;
    padding: 33px 40px 30px;
}

.donate-now__causes-title {
    font-size: 24px;
    line-height: 34px;
    margin-bottom: 12px;
}

.donate-now__causes-title a {
    color: var(--sopot-black);
    -webkit-transition: all 500ms ease;
    transition: all 500ms ease;
}

.donate-now__causes-title a:hover {
    color: var(--sopot-base);
}

.donate-now__causes-text {
    font-size: 16px;
    line-height: 30px;
    margin: 0;
}

.donate-now__progress {
    position: relative;
    display: block;
    width: 100%;
    padding: 53px 40px 29px;
    background-color: rgba(var(--sopot-base-rgb), .10);
}

.donate-now__progress .bar {
    position: relative;
    width: 100%;
    height: 4px;
    background: var(--sopot-white);
    border-radius: 0px;
    margin-bottom: 12px;
}

.donate-now__progress .bar-inner {
    position: relative;
    display: block;
    width: 0px;
    height: 4px;
    border-radius: 0px;
    background: var(--sopot-base);
    -webkit-transition: all 1500ms ease;
    -ms-transition: all 1500ms ease;
    -o-transition: all 1500ms ease;
    -moz-transition: all 1500ms ease;
    transition: all 1500ms ease;
}

.donate-now__progress .count-text {
    position: absolute;
    right: -40px;
    bottom: 16px;
    color: var(--sopot-white);
    line-height: 20px;
    font-size: 10px;
    text-align: center;
    font-weight: 400;
    opacity: 0;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
    -moz-transition: all 500ms ease;
    transition: all 500ms ease;
    height: 20px;
    width: 40px;
    background: var(--sopot-base);
}

.donate-now__progress .bar-inner.counted .count-text {
    opacity: 1;
}

.donate-now__progress .count-text:after {
    content: '';
    position: absolute;
    left: 0;
    top: 100%;
    border-right: 6px solid transparent;
    border-top: 4px solid var(--sopot-base);
}

.donate-now__goals {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.donate-now__goals>p {
    font-size: 14px;
    margin: 0;
}

.donate-now__goals>p>span {
    font-size: 18px;
    color: var(--sopot-black);
    font-weight: 500;
    line-height: 28px;
}

.donation-details__organizer {
    position: relative;
    display: flex;
    background-color: rgba(var(--sopot-base-rgb), .10);
    padding: 50px 50px 41px;
}

.donation-details__organizer-img {
    position: relative;
    display: block;
}

.donation-details__organizer-img img {
    width: auto;
}

.donation-details__organizer-content {
    margin-left: 20px;
    margin-top: -7px;
}

.donation-details__organizer-date {
    font-size: 12px;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    line-height: 22px;
    font-weight: 500;
}

.donation-details__organizer-title {
    color: var(--sopot-black);
    font-weight: 700;
    line-height: 28px;
    margin: 0;
    padding-top: 3px;
    padding-bottom: 2px;
}

.donation-details__organizer-name {
    color: var(--sopot-base);
    font-weight: 700;
    line-height: 28px;
    margin: 0;
}

.donation-details__organizer-list {
    position: relative;
    display: block;
    margin-top: 11px;
}

.donation-details__organizer-list li {
    position: relative;
    display: flex;
    align-items: center;
}


.donation-details__organizer-list li .icon span {
    font-size: 13px;
    color: var(--sopot-gray);
}

.donation-details__organizer-list li .text {
    margin-left: 10px;
}

.donation-details__organizer-list li .text p {
    font-size: 16px;
    font-weight: 500;
    margin: 0;
}

/*--------------------------------------------------------------
## Donations Details
--------------------------------------------------------------*/
.donation-details {
    position: relative;
    display: block;
    padding: 120px 0 120px;
}

.donation-details__left {
    position: relative;
    display: block;
}

.donation-details__top {
    position: relative;
    display: block;
    margin-bottom: 51px;
}

.donation-details__top-img {
    position: relative;
    display: block;
}

.donation-details__top-img img {
    width: 100%;
}

.donation-details__category {
    position: absolute;
    bottom: 30px;
    left: 40px;
    background-color: var(--sopot-base);
    padding: 0 20px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    z-index: 3;
}

.donation-details__category span {
    font-size: 10px;
    color: var(--sopot-white);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-weight: 700;
}

.donation-details__top-progress-box {
    position: relative;
    display: block;
    background-color: rgba(var(--sopot-base-rgb), .10);
    padding: 50px 40px 50px;
}

.donation-details__progress {
    position: relative;
    display: block;
}

.donation-details__progress .bar {
    position: relative;
    width: 100%;
    height: 4px;
    background: var(--sopot-white);
    border-radius: 0px;
    margin-bottom: 12px;
}

.donation-details__progress .bar-inner {
    position: relative;
    display: block;
    width: 0px;
    height: 4px;
    border-radius: 0px;
    background: var(--sopot-base);
    -webkit-transition: all 1500ms ease;
    -ms-transition: all 1500ms ease;
    -o-transition: all 1500ms ease;
    -moz-transition: all 1500ms ease;
    transition: all 1500ms ease;
}

.donation-details__progress .count-text {
    position: absolute;
    right: -40px;
    bottom: 16px;
    color: var(--sopot-white);
    line-height: 20px;
    font-size: 10px;
    text-align: center;
    font-weight: 400;
    opacity: 0;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
    -moz-transition: all 500ms ease;
    transition: all 500ms ease;
    height: 20px;
    width: 40px;
    background: var(--sopot-base);
}

.donation-details__progress .bar-inner.counted .count-text {
    opacity: 1;
}

.donation-details__progress .count-text:after {
    content: '';
    position: absolute;
    left: 0;
    top: 100%;
    border-right: 6px solid transparent;
    border-top: 4px solid var(--sopot-base);
}

.donation-details__goals {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.donation-details__goals>p {
    font-size: 14px;
    margin: 0;
}

.donation-details__goals>p>span {
    font-size: 18px;
    color: var(--sopot-black);
    font-weight: 500;
    line-height: 28px;
}

.donation-details__top-donate-btn-box {
    position: relative;
    display: block;
    margin-top: 23px;
}

.donation-details__content {
    position: relative;
    display: block;
}

.donation-details__title {
    font-size: 30px;
    line-height: 40px;
    margin-bottom: 23px;
}

.donation-details__text-1 {
    margin: 0;
    padding-bottom: 44px;
}

.donation-details__text-2 {
    margin: 0;
}

.donation-details__points {
    position: relative;
    display: block;
    margin-top: 45px;
    margin-bottom: 45px;
}

.donation-details__points li {
    position: relative;
    display: flex;
    align-items: center;
}

.donation-details__points li+li {
    margin-top: 10px;
}

.donation-details__points li .icon {
    position: relative;
    display: flex;
    align-items: center;
}

.donation-details__points li .icon span {
    font-size: 20px;
    color: var(--sopot-base);
}

.donation-details__points li .text {
    margin-left: 15px;
}

.donation-details__points li .text p {
    font-size: 20px;
    color: var(--sopot-black);
    margin: 0;
}

.donation-details__content-img-boxes {
    position: relative;
    display: block;
    margin-bottom: 12px;
}

.donation-details__content-img-single {
    position: relative;
    display: block;
    margin-bottom: 30px;
}

.donation-details__content-img-single img {
    width: 100%;
    border-radius: var(--sopot-bdr-radius);
}

.donation-details__text-3 {
    margin: 0;
}

.donation-details__presentation {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: rgba(var(--sopot-base-rgb), .10);
    padding: 56px 60px 55px;
    margin-top: 49px;
    margin-bottom: 54px;
}

.donation-details__presentation-icon-box {
    position: relative;
    display: flex;
    align-items: center;
}

.donation-details__presentation-icon-box span {
    font-size: 62px;
    color: var(--sopot-base);
}

.donation-details__presentation-icon-box h4 {
    font-size: 24px;
    line-height: 34px;
    margin-left: 10px;
}


.donation-details__recent-donation {
    position: relative;
    display: block;
    background-color: rgba(var(--sopot-base-rgb), .10);
    padding-top: 42px;
    padding-left: 50px;
    padding-right: 30px;
    padding-bottom: 40px;
}

.donation-details__recent-donation-title {
    font-size: 20px;
    line-height: 30px;
    margin-bottom: 22px;
}

.donation-details__recent-donation-list {
    position: relative;
    display: block;
}

.donation-details__recent-donation-list li {
    position: relative;
    display: flex;
    border-bottom: 1px solid var(--sopot-white);
    padding-bottom: 20px;
    margin-bottom: 30px;
}

.donation-details__recent-donation-list li:last-child {
    border-bottom: 0;
    padding-bottom: 0;
    margin-bottom: 0;
}

.donation-details__recent-donation-img {
    position: relative;
    display: block;
}

.donation-details__recent-donation-img img {
    width: auto;
}

.donation-details__recent-donation-content {
    margin-left: 20px;
    margin-top: -6px;
}

.donation-details__recent-donation-amount {
    font-size: 20px;
    color: var(--sopot-base);
    line-height: 30px;
    margin-bottom: 4px;
}

.donation-details__recent-donation-name {
    font-size: 16px;
    color: var(--sopot-black);
    font-weight: 500;
    margin: 0;
    line-height: 26px;
}

.donation-details__recent-donation-name span {
    font-size: 12px;
    color: var(--sopot-gray);
    text-transform: uppercase;
    font-weight: 400;
    letter-spacing: 0.1em;
}

.donation-details__recent-donation-text {
    font-size: 16px;
    margin: 0;
    line-height: 30px;
}

/*--------------------------------------------------------------
# Testimonial Page
--------------------------------------------------------------*/
.testimonials-page {
    position: relative;
    display: block;
    padding: 120px 0 90px;
}

.testimonials-page .testimonial-one__single {
    margin-bottom: 30px;
}


/*--------------------------------------------------------------
# Gallery Page
--------------------------------------------------------------*/
.gallery-page {
    position: relative;
    display: block;
    padding: 120px 0 90px;
}

.gallery-page__single {
    position: relative;
    display: block;
    margin-bottom: 30px;
}

.gallery-page__img {
    position: relative;
    display: block;
    border-radius: var(--sopot-bdr-radius);
    overflow: hidden;
    z-index: 1;
}

.gallery-page__img:before {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    content: "";
    background-color: rgba(var(--sopot-base-rgb), 0.9);
    -webkit-transition: all 700ms ease;
    transition: all 700ms ease;
    -webkit-transform: translateY(-102%);
    transform: translateY(-102%);
    border-radius: var(--sopot-bdr-radius);
    z-index: 1;
}

.gallery-page__single:hover .gallery-page__img:before {
    -webkit-transform: translateY(0%);
    transform: translateY(0%);
}

.gallery-page__img img {
    width: 100%;
    border-radius: var(--sopot-bdr-radius);
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transition: -webkit-transform 500ms ease;
    transition: -webkit-transform 500ms ease;
    transition: transform 500ms ease;
    transition: transform 500ms ease, -webkit-transform 500ms ease;
}

.gallery-page__single:hover .gallery-page__img img {
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
}

.gallery-page__icon {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    z-index: 2;
}

.gallery-page__icon a {
    height: 80px;
    width: 80px;
    background-color: var(--sopot-black);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 22px;
    color: var(--sopot-white);
    -webkit-transform: translateY(100px);
    transform: translateY(100px);
    opacity: 0;
    -webkit-transition: all 700ms ease;
    transition: all 700ms ease;
    z-index: 2;
}

.gallery-page__single:hover .gallery-page__icon a {
    -webkit-transform: translateY(0px);
    transform: translateY(0px);
    opacity: 1;
}

.gallery-page__icon a:hover {
    background-color: var(--sopot-white);
    color: var(--sopot-base);
}



.landing {
  display: block;              /* Makes margin:auto work */
  margin: 0 auto;              /* Centers horizontally */
  max-width: 500px;            /* Set your max width */
  max-height: 500px;           /* Set your max height */
  width: 100%;                 /* Shrinks proportionally */
  height: auto;                /* Maintains aspect ratio */
  object-fit: contain;         /* Prevents distortion */
}










/*--------------------------------------------------------------
# Two Section
--------------------------------------------------------------*/