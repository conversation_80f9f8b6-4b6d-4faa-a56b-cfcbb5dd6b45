<?php
header('Content-Type: application/json');

// Enable error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log all requests for debugging
error_log("Signup request received: " . print_r($_POST, true));

// Include database connection
require_once 'database.php';

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    $error = ['success' => false, 'message' => 'Method not allowed', 'debug' => 'Request method: ' . $_SERVER['REQUEST_METHOD']];
    error_log("Signup error: " . json_encode($error));
    echo json_encode($error);
    exit;
}

// Get form data
$firstName = isset($_POST['first_name']) ? trim($_POST['first_name']) : '';
$lastName = isset($_POST['last_name']) ? trim($_POST['last_name']) : '';
$email = isset($_POST['email']) ? trim($_POST['email']) : '';

// Log received data for debugging
error_log("Received data - First: $firstName, Last: $lastName, Email: $email");

// Validate required fields
if (empty($firstName) || empty($lastName) || empty($email)) {
    $error = [
        'success' => false,
        'message' => 'All fields are required',
        'debug' => [
            'first_name' => empty($firstName) ? 'missing' : 'present',
            'last_name' => empty($lastName) ? 'missing' : 'present',
            'email' => empty($email) ? 'missing' : 'present'
        ]
    ];
    error_log("Validation error: " . json_encode($error));
    echo json_encode($error);
    exit;
}

// Validate email format
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $error = ['success' => false, 'message' => 'Invalid email format', 'debug' => 'Email: ' . $email];
    error_log("Email validation error: " . json_encode($error));
    echo json_encode($error);
    exit;
}

try {
    // Test database connection
    if (!isset($pdo)) {
        throw new Exception("Database connection not established");
    }

    // Check if email already exists
    $checkStmt = $pdo->prepare("SELECT id FROM signups WHERE email = ?");
    $checkStmt->execute([$email]);

    if ($checkStmt->rowCount() > 0) {
        $error = ['success' => false, 'message' => 'Email already registered', 'debug' => 'Duplicate email: ' . $email];
        error_log("Duplicate email error: " . json_encode($error));
        echo json_encode($error);
        exit;
    }

    // Insert new signup
    $stmt = $pdo->prepare("INSERT INTO signups (first_name, last_name, email, created_at) VALUES (?, ?, ?, NOW())");
    $result = $stmt->execute([$firstName, $lastName, $email]);

    if ($result) {
        $success = ['success' => true, 'message' => 'Successfully signed up!', 'debug' => 'Insert ID: ' . $pdo->lastInsertId()];
        error_log("Signup success: " . json_encode($success));
        echo json_encode($success);
    } else {
        throw new Exception("Failed to insert record");
    }

} catch(PDOException $e) {
    $error = [
        'success' => false,
        'message' => 'Database error occurred',
        'debug' => [
            'error' => $e->getMessage(),
            'code' => $e->getCode(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ];
    error_log("Database error: " . json_encode($error));
    echo json_encode($error);
} catch(Exception $e) {
    $error = [
        'success' => false,
        'message' => 'An error occurred',
        'debug' => [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ];
    error_log("General error: " . json_encode($error));
    echo json_encode($error);
}
?>
