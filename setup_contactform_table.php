<?php
// Setup script to create the contactform table
// Run this file once to create the database table

// Include database connection
require_once 'assets/inc/database.php';

try {
    // Create the contactform table
    $sql = "CREATE TABLE IF NOT EXISTS contactform (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        phone VARCHAR(50),
        subject VARCHAR(500),
        message TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        ip_address VARCHAR(45),
        INDEX idx_email (email),
        INDEX idx_created_at (created_at)
    )";
    
    $pdo->exec($sql);
    
    // Add table comment
    $pdo->exec("ALTER TABLE contactform COMMENT = 'Stores contact form submissions from the website'");
    
    echo "<h2>Database Setup Complete!</h2>";
    echo "<p>✅ The 'contactform' table has been created successfully.</p>";
    echo "<p>Your contact form is now ready to save submissions to the database.</p>";
    
    // Test the table by showing its structure
    $stmt = $pdo->query("DESCRIBE contactform");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Table Structure:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 20px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>Test your contact form to make sure it's working</li>";
    echo "<li>Check the 'contactform' table in your database to see submitted data</li>";
    echo "<li>You can delete this setup file (setup_contactform_table.php) after running it</li>";
    echo "</ul>";
    
} catch(PDOException $e) {
    echo "<h2>Database Setup Error</h2>";
    echo "<p>❌ Error creating table: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database connection and try again.</p>";
} catch(Exception $e) {
    echo "<h2>Setup Error</h2>";
    echo "<p>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
