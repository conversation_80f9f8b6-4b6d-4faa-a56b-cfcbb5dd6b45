<?php
// Simple admin page to view contact form submissions
// Include database connection
require_once 'assets/inc/database.php';

// Simple authentication (you should improve this for production)
$admin_password = "admin123"; // Change this password!
$authenticated = false;

if (isset($_POST['password'])) {
    if ($_POST['password'] === $admin_password) {
        $authenticated = true;
        session_start();
        $_SESSION['admin_authenticated'] = true;
    }
} elseif (isset($_SESSION['admin_authenticated']) && $_SESSION['admin_authenticated']) {
    $authenticated = true;
}

if (!$authenticated) {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Contact Form Admin</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 400px; margin: 100px auto; padding: 20px; }
            input[type="password"], input[type="submit"] { width: 100%; padding: 10px; margin: 10px 0; }
            .error { color: red; }
        </style>
    </head>
    <body>
        <h2>Contact Form Admin Login</h2>
        <?php if (isset($_POST['password'])): ?>
            <p class="error">Invalid password</p>
        <?php endif; ?>
        <form method="post">
            <input type="password" name="password" placeholder="Enter admin password" required>
            <input type="submit" value="Login">
        </form>
        <p><small>Default password: admin123 (change this in the code!)</small></p>
    </body>
    </html>
    <?php
    exit;
}

// Fetch contact form submissions
try {
    $stmt = $pdo->query("SELECT * FROM contactform ORDER BY created_at DESC");
    $contacts = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    die("Error fetching contacts: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Contact Form Submissions</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .message { max-width: 300px; word-wrap: break-word; }
        .header { display: flex; justify-content: space-between; align-items: center; }
        .logout { background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; }
        .count { color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Contact Form Submissions</h1>
        <a href="?logout=1" class="logout">Logout</a>
    </div>
    
    <?php if (isset($_GET['logout'])): session_destroy(); header('Location: view_contacts.php'); exit; endif; ?>
    
    <p class="count">Total submissions: <?php echo count($contacts); ?></p>
    
    <?php if (empty($contacts)): ?>
        <p>No contact form submissions yet.</p>
    <?php else: ?>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Date</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Subject</th>
                    <th>Message</th>
                    <th>IP Address</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($contacts as $contact): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($contact['id']); ?></td>
                        <td><?php echo htmlspecialchars($contact['created_at']); ?></td>
                        <td><?php echo htmlspecialchars($contact['name']); ?></td>
                        <td><a href="mailto:<?php echo htmlspecialchars($contact['email']); ?>"><?php echo htmlspecialchars($contact['email']); ?></a></td>
                        <td><?php echo htmlspecialchars($contact['phone']); ?></td>
                        <td><?php echo htmlspecialchars($contact['subject']); ?></td>
                        <td class="message"><?php echo nl2br(htmlspecialchars($contact['message'])); ?></td>
                        <td><?php echo htmlspecialchars($contact['ip_address']); ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php endif; ?>
    
    <p><small>Last updated: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
