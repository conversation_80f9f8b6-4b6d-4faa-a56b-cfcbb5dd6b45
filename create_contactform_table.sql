-- Create contactform table for storing contact form submissions
-- Run this SQL in your database to create the table

CREATE TABLE IF NOT EXISTS contactform (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    subject <PERSON><PERSON><PERSON><PERSON>(500),
    message TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    INDEX idx_email (email),
    INDEX idx_created_at (created_at)
);

-- Optional: Add a comment to the table
ALTER TABLE contactform COMMENT = 'Stores contact form submissions from the website';
