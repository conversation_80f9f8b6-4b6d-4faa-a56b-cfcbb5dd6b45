<?php
// PHP mail() Email Handler for Give To Jamaica
// Sends to admin and also a thank-you email to sender

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log all requests for debugging
error_log("Contact form request received: " . print_r($_POST, true));

// Read and sanitize form values
$name        = isset($_POST['name']) ? trim(strip_tags($_POST['name'])) : "";
$senderEmail = isset($_POST['email']) ? trim(strip_tags($_POST['email'])) : "";
$phone       = isset($_POST['phone']) ? trim(strip_tags($_POST['phone'])) : "";
$services    = isset($_POST['services']) ? trim(strip_tags($_POST['services'])) : "";
$subject     = isset($_POST['subject']) ? trim(strip_tags($_POST['subject'])) : "";
$address     = isset($_POST['address']) ? trim(strip_tags($_POST['address'])) : "";
$website     = isset($_POST['website']) ? trim(strip_tags($_POST['website'])) : "";
$message     = isset($_POST['message']) ? trim(strip_tags($_POST['message'])) : "";

// Log sanitized data
error_log("Sanitized data - Name: $name, Email: $senderEmail, Message length: " . strlen($message));

// Validate required fields
$errors = [];
if (empty($name)) {
    $errors[] = "Name is required";
}
if (empty($senderEmail)) {
    $errors[] = "Email is required";
} elseif (!filter_var($senderEmail, FILTER_VALIDATE_EMAIL)) {
    $errors[] = "Invalid email format";
}
if (empty($message)) {
    $errors[] = "Message is required";
}

// If there are validation errors, display them
if (!empty($errors)) {
    $errorMsg = implode(", ", $errors);
    error_log("Contact form validation errors: " . $errorMsg);
    echo "<div class='inner error'><p class='error'>Validation errors: $errorMsg</p></div>";
    exit;
}

// Recipient info
$to       = "<EMAIL>";
$fromName = "Give To Jamaica";

// Build admin email subject
$mailSubject = 'Contact request from ' . $name . ' - Give To Jamaica Website';

// Build admin email body
$body  = "New contact form submission from Give To Jamaica website:\n";
$body .= "=================================================\n\n";
$body .= "SENDER INFORMATION:\n";
$body .= "Name: " . $name . "\n";
$body .= "Email: " . $senderEmail . " (REPLY TO THIS EMAIL)\n";

if ($phone)   { $body .= "Phone: " . $phone . "\n"; }
if ($services){ $body .= "Services: " . $services . "\n"; }
if ($subject) { $body .= "Subject: " . $subject . "\n"; }
if ($address) { $body .= "Address: " . $address . "\n"; }
if ($website) { $body .= "Website: " . $website . "\n"; }

$body .= "\nMESSAGE:\n";
$body .= $message;
$body .= "\n\n---\n";
$body .= "Sent from Give To Jamaica contact form\n";
$body .= "Time: " . date('Y-m-d H:i:s T') . "\n";
$body .= "IP: " . $_SERVER['REMOTE_ADDR'] . "\n";

// Set headers for admin email
$headers  = "From: $fromName <$to>\r\n";
$headers .= "Reply-To: $senderEmail\r\n";
$headers .= "X-Mailer: PHP/" . phpversion();

error_log("Attempting to send email using mail()");

// Send email to admin
$adminSent = mail($to, $mailSubject, $body, $headers);


?>
