<?php
// Contact form handler - saves to database and sends email
header('Content-Type: text/html; charset=UTF-8');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database connection
require_once 'database.php';

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo "<div class='inner error'><p class='error'>Invalid request method</p></div>";
    exit;
}

// Sanitize and validate input data
$name = isset($_POST['name']) ? trim(preg_replace("/[^\.\-\' a-zA-Z0-9]/", "", $_POST['name'])) : "";
$email = isset($_POST['email']) ? trim(preg_replace("/[^\.\-\_\@a-zA-Z0-9]/", "", $_POST['email'])) : "";
$phone = isset($_POST['phone']) ? trim(preg_replace("/[^\.\-\_\@a-zA-Z0-9\(\)\+\s]/", "", $_POST['phone'])) : "";
$subject = isset($_POST['subject']) ? trim(preg_replace("/[^\.\-\_\@a-zA-Z0-9\s]/", "", $_POST['subject'])) : "";
$message = isset($_POST['message']) ? trim($_POST['message']) : "";

// Log received data for debugging
error_log("Contact form data - Name: $name, Email: $email, Phone: $phone, Subject: $subject, Message length: " . strlen($message));

// Validate required fields
$errors = [];
if (empty($name)) {
    $errors[] = "Name is required";
}
if (empty($email)) {
    $errors[] = "Email is required";
} elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $errors[] = "Invalid email format";
}
if (empty($message)) {
    $errors[] = "Message is required";
}

// If there are validation errors, display them
if (!empty($errors)) {
    $errorMsg = implode(", ", $errors);
    error_log("Contact form validation errors: " . $errorMsg);
    echo "<div class='inner error'><p class='error'>Validation errors: $errorMsg</p></div>";
    exit;
}

try {
    // First, save to database
    $stmt = $pdo->prepare("INSERT INTO contactform (name, email, phone, subject, message, created_at, ip_address) VALUES (?, ?, ?, ?, ?, NOW(), ?)");
    $result = $stmt->execute([
        $name,
        $email,
        $phone,
        $subject,
        $message,
        $_SERVER['REMOTE_ADDR']
    ]);

    if (!$result) {
        throw new Exception("Failed to save contact form data to database");
    }

    $contactId = $pdo->lastInsertId();
    error_log("Contact form saved to database with ID: " . $contactId);

    // Now send email (using existing email logic)
    $to = "<EMAIL>";
    $fromName = "Give To Jamaica";
    
    // Build email subject
    $mailSubject = 'Contact request from ' . $name . ' - Give To Jamaica Website';
    
    // Build email body
    $body = "New contact form submission from Give To Jamaica website:\n";
    $body .= "=================================================\n\n";
    $body .= "SENDER INFORMATION:\n";
    $body .= "Name: " . $name . "\n";
    $body .= "Email: " . $email . " (REPLY TO THIS EMAIL)\n";
    
    if ($phone) { $body .= "Phone: " . $phone . "\n"; }
    if ($subject) { $body .= "Subject: " . $subject . "\n"; }
    
    $body .= "\nMESSAGE:\n";
    $body .= $message;
    $body .= "\n\n---\n";
    $body .= "Sent from Give To Jamaica contact form\n";
    $body .= "Time: " . date('Y-m-d H:i:s T') . "\n";
    $body .= "IP: " . $_SERVER['REMOTE_ADDR'] . "\n";
    $body .= "Database ID: " . $contactId . "\n";
    
    // Set headers for email
    $headers = "From: $fromName <$to>\r\n";
    $headers .= "Reply-To: $email\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion();
    
    // Attempt to send email
    $emailSent = mail($to, $mailSubject, $body, $headers);
    
    if ($emailSent) {
        error_log("Contact form email sent successfully for ID: " . $contactId);
    } else {
        error_log("Contact form email failed to send for ID: " . $contactId);
        // Don't fail the whole process if email fails, since data is already saved
    }
    
    // Success response
    echo "<div class='inner success'><p class='success'>Thanks for contacting us! Your message has been received and we will contact you ASAP!</p></div>";

} catch(PDOException $e) {
    error_log("Database error in contact form: " . $e->getMessage());
    echo "<div class='inner error'><p class='error'>Sorry, there was a problem saving your message. Please try again or contact us <NAME_EMAIL></p></div>";
} catch(Exception $e) {
    error_log("General error in contact form: " . $e->getMessage());
    echo "<div class='inner error'><p class='error'>Sorry, there was a problem processing your message. Please try again or contact us <NAME_EMAIL></p></div>";
}
?>
